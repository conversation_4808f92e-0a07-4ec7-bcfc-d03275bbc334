<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\config\tasks\Res;
use app\back\entities\RefTypeRule;
use app\back\modules\task\actions\update\RefcodesRefTypeTask;
use app\back\repositories\Refcodes;
use app\back\repositories\RefTypeRules;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;

#[CoversClass(RefcodesRefTypeTask::class)]
class RefcodesRefTypeTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    #[DataProvider('data')]
    public function testUpdateRefType(string $refcode, array $refTypeRule, ?int $expectedType): void
    {

        $this->haveRefcodeRecord(['code' => $refcode]);
        list($value, $operator, $refType) = $refTypeRule;
        $this->haveRecord(RefTypeRules::class, ['value' => $value, 'value_operator' => $operator, 'ref_type' => $refType, 'created_by' => 1]);

        $this->runTask('update-refcodes-ref-type', Res::DEFAULT);

        $this->seeRecordWithFields(
            Refcodes::class,
            ['code' => $refcode],
            ['ref_type' => $expectedType]
        );
    }

    public static function data(): array
    {
        return [
            ['yandex|http://www.gaminatorslots.com/lobby/', ['yandex|', RefTypeRule::OPERATOR_PREFIX, RefTypeRule::RT_DIRECT_SEO], RefTypeRule::RT_DIRECT_SEO],
            ['facebook|http://www.gaminatorslots.com/lobby/', ['yandex|', RefTypeRule::OPERATOR_PREFIX, RefTypeRule::RT_DIRECT_SEO], null],
            ['vp_w18761c193316l18208gptp2062_cgx1512895022223401p12', ['vp_w18761', RefTypeRule::OPERATOR_PREFIX, RefTypeRule::RT_SEO], RefTypeRule::RT_SEO],
            ['vp_w18762c193316l18208gptp2062', ['vp_w18761', RefTypeRule::OPERATOR_PREFIX, RefTypeRule::RT_SEO], null],
            ['aff_84c4e9_3_Mediabuy', ['Mediabuy', RefTypeRule::OPERATOR_CONTAINS, RefTypeRule::RT_BRANDING], RefTypeRule::RT_BRANDING],
            ['aff_84c4e9_3_Direct', ['Mediabuy', RefTypeRule::OPERATOR_CONTAINS, RefTypeRule::RT_BRANDING], null],
            ['vp_w172860c148150l11872p1501_facebook_0tfigediw5', ['facebook', RefTypeRule::OPERATOR_CONTAINS, RefTypeRule::RT_FB], RefTypeRule::RT_FB],
            ['vp_w172860c148150l11872p1501_0tfigediw5', ['facebook', RefTypeRule::OPERATOR_CONTAINS, RefTypeRule::RT_FB], null],
        ];
    }
}
