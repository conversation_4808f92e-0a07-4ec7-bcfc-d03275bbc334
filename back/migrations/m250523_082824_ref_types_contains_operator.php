<?php

declare(strict_types=1);

namespace app\back\migrations;

use app\back\entities\RefTypeRule;

class m250523_082824_ref_types_contains_operator extends BaseMigration
{
    public function safeUp(): void
    {
        $this->sql("ALTER TABLE ref_types_rules RENAME COLUMN prefix TO value");
        $this->sql("ALTER TABLE ref_types_rules ADD COLUMN value_operator SMALLINT NOT NULL DEFAULT 1");
    }

    public function safeDown(): void
    {
        $this->sql("ALTER TABLE ref_types_rules RENAME COLUMN value TO prefix");
        $this->sql("ALTER TABLE ref_types_rules DROP COLUMN value_operator");
    }
}
