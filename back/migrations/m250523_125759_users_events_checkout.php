<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250523_125759_users_events_checkout extends BaseMigration
{
    private const string TABLE_NAME = 'users_events_checkout';

    public function safeUp(): void
    {
        $this->db->createCommand()->createTable(self::TABLE_NAME, [
            'event_id' => 'varchar(32) not null constraint ' . self::TABLE_NAME . '_pk primary key',
            'site_id' => 'integer',
            'user_id' => 'bigint',
            'event_type' => 'smallint not null',
            'context_id' => 'varchar(32)',
            'client_created_at' => 'timestamp(6) not null',
            'server_received_at' => 'timestamp(6) not null',
            'inserted_at' => 'timestamp(6) default now() not null',
            'pay_sys_id' => 'integer',
            'product_open_id' => 'varchar(36)',
            'ip' => 'inet',
            'useragent_id' => 'integer',
            'instance_id' => 'smallint',
        ])->execute();

        $this->db->createCommand()->createIndex(self::TABLE_NAME, self::TABLE_NAME . '_context_id', ['context_id'])
            ->execute();
        $this->db->createCommand()->createIndex(self::TABLE_NAME, self::TABLE_NAME . '_site_id_user_id', ['site_id', 'user_id'])
            ->execute();
        $this->db->createCommand()->createIndex(self::TABLE_NAME, self::TABLE_NAME . '_inserted_at', ['inserted_at'])
            ->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->dropTable(self::TABLE_NAME)->execute();
    }
}
