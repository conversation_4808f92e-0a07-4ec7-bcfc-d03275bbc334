<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250522_142946_vip_thresholds_new_columns extends BaseMigration
{
    public function safeUp(): void
    {
        $this->sql("ALTER TABLE users_statuses_vips_thresholds ADD COLUMN site_id int NOT NULL DEFAULT -1");
        $this->sql("ALTER TABLE users_statuses_vips_thresholds ADD COLUMN is_active boolean NOT NULL DEFAULT TRUE");
        $this->sql("ALTER TABLE users_statuses_vips_thresholds ADD COLUMN is_up boolean NOT NULL DEFAULT TRUE");
        $this->sql("ALTER TABLE users_statuses_vips_thresholds ADD COLUMN period interval NOT NULL DEFAULT 'P30D'");
        $this->sql("ALTER TABLE users_statuses_vips_thresholds ADD COLUMN check_period smallint NOT NULL DEFAULT 3");

        $this->sql("ALTER TABLE users_statuses_vips_thresholds ALTER COLUMN period DROP DEFAULT");
        $this->sql("ALTER TABLE users_statuses_vips_thresholds ALTER COLUMN check_period DROP DEFAULT");
        $this->sql("ALTER TABLE users_statuses_vips_thresholds ALTER COLUMN site_id DROP DEFAULT");

        $this->sql("ALTER TABLE users_statuses_vips_thresholds RENAME COLUMN in_amount_up TO amount");
        $this->sql("ALTER TABLE users_statuses_vips_thresholds DROP COLUMN in_amount_down_multiplier");

        $this->sql("ALTER TABLE users_statuses_vips_thresholds DROP CONSTRAINT users_statuses_vips_thresholds_country_status_active_status");
        $this->sql("ALTER TABLE users_statuses_vips_thresholds ADD CONSTRAINT users_statuses_vips_thresholds_unique UNIQUE (site_id, country, status, active_status, is_up, period)");

        $this->sql("UPDATE users_statuses_vips_thresholds SET active_status = 1 WHERE active_status = -1");
    }

    public function safeDown(): void
    {
        $this->sql("ALTER TABLE users_statuses_vips_thresholds RENAME COLUMN amount TO in_amount_up");
        $this->sql("ALTER TABLE users_statuses_vips_thresholds ADD COLUMN in_amount_down_multiplier integer");

        $this->sql("ALTER TABLE users_statuses_vips_thresholds DROP COLUMN site_id");
        $this->sql("ALTER TABLE users_statuses_vips_thresholds DROP COLUMN is_active");
        $this->sql("ALTER TABLE users_statuses_vips_thresholds DROP COLUMN is_up");
        $this->sql("ALTER TABLE users_statuses_vips_thresholds DROP COLUMN period");
        $this->sql("ALTER TABLE users_statuses_vips_thresholds DROP COLUMN check_period");

        $this->sql("ALTER TABLE users_statuses_vips_thresholds ADD CONSTRAINT users_statuses_vips_thresholds_country_status_active_status UNIQUE (country, status, active_status)");
    }
}
