<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250522_115613_users_documents_page_num extends BaseMigration
{
    public function up(): void
    {
        $this->sql('ALTER TABLE users_documents ADD COLUMN page_num smallint NOT NULL DEFAULT 0');
        $this->sql('CREATE UNIQUE INDEX CONCURRENTLY users_documents_site_id_external_id_page_num_uniq ON users_documents (site_id, external_id, page_num)');
        $this->sql('DROP INDEX users_documents_site_id_external_id_uniq');
    }

    public function down(): void
    {
        // for dev
        $this->sql('DELETE FROM users_documents WHERE page_num > 0');

        $this->sql('CREATE UNIQUE INDEX CONCURRENTLY users_documents_site_id_external_id_uniq ON users_documents (site_id, external_id)');
        $this->sql('DROP INDEX users_documents_site_id_external_id_page_num_uniq');
        $this->sql('ALTER TABLE "users_documents" DROP COLUMN page_num');
    }
}
