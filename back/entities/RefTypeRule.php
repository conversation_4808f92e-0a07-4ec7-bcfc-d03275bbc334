<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\StringValidator;

class RefTypeRule extends BaseEntity
{
    public const int RT_DIRECT_SEO = 1;
    public const int RT_SEO = 2;
    public const int RT_PPC = 3;
    public const int RT_FB = 4;
    public const int RT_SEO_APP = 5;
    public const int RT_YOUTUBE = 6;
    public const int RT_IN_APP = 7;
    public const int RT_UAC = 8;
    public const int RT_BRANDING = 9;
    public const int RT_SPAM = 10;
    public const int RT_CONTEXT = 11;
    public const int RT_SOCIAL = 12;

    public const string GROUP_UNKNOWN = 'unknown';
    public const string GROUP_SEO = 'seo';
    public const string GROUP_TRUSTED = 'trusted';
    public const string GROUP_GENERAL = 'general';

    public const int OPERATOR_PREFIX = 1;
    public const int OPERATOR_CONTAINS = 2;

    public const array OPERATORS = [
        self::OPERATOR_PREFIX => 'Prefix',
        self::OPERATOR_CONTAINS => 'Contains',
    ];

    public const array REF_TYPES = [
        self::RT_DIRECT_SEO => 'Direct SEO',
        self::RT_SEO => 'SEO',
        self::RT_PPC => 'PPC',
        self::RT_FB => 'Facebook',
        self::RT_SEO_APP => 'SEO app',
        self::RT_YOUTUBE => 'Youtube',
        self::RT_IN_APP => 'In app',
        self::RT_UAC => 'UAC',
        self::RT_BRANDING => 'Branding',
        self::RT_SPAM => 'SPAM',
        self::RT_CONTEXT => 'Context',
        self::RT_SOCIAL => 'Social',
    ];

    public const array REF_TYPES_GROUPS = [
        self::RT_DIRECT_SEO => self::GROUP_SEO,
        self::RT_SEO => self::GROUP_SEO,
        self::RT_PPC => self::GROUP_SEO,
        self::RT_FB => self::GROUP_TRUSTED,
        self::RT_SEO_APP => self::GROUP_SEO,
        self::RT_YOUTUBE => self::GROUP_GENERAL,
        self::RT_IN_APP => self::GROUP_TRUSTED,
        self::RT_UAC => self::GROUP_TRUSTED,
        self::RT_BRANDING => self::GROUP_GENERAL,
        self::RT_SPAM => self::GROUP_GENERAL,
        self::RT_CONTEXT => self::GROUP_SEO,
        self::RT_SOCIAL => self::GROUP_GENERAL,
    ];

    #[IdValidator]
    public int $id;
    #[StringValidator(max:255)]
    public string $value;
    #[IntInArrayValidator(self::OPERATORS)]
    public int $value_operator;
    #[IntInArrayValidator(self::REF_TYPES)]
    public int $ref_type;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[IdValidator]
    public int $created_by;

    public static function getRefTypeName(int $refType): ?string
    {
        return self::REF_TYPES[$refType] ?? null;
    }

    public static function getGroupByRefType(?int $refType): ?string
    {
        return self::REF_TYPES_GROUPS[$refType] ?? null;
    }
}
