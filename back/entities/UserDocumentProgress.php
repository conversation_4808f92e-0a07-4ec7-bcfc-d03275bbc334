<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\PgArray;
use app\back\components\validators\BigIdValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\JsonArrayOfObjectsValidator;
use app\back\components\validators\PgArrayValidator;
use app\back\components\kyc\KycDetailsRequestModel;

class UserDocumentProgress extends BaseEntity
{
    public const int REASON_LOW_QUALITY = 1;
    public const int REASON_SMALL_RESOLUTION = 2;
    public const int REASON_CROPPED = 3;
    public const int REASON_TOO_FAR = 4;
    public const int REASON_OVERLIGHTED = 5;
    public const int REASON_PASSPORT_NOT_FULL = 6;
    public const int REASON_PASSPORT_SERIAL_AND_NUM_FULLY_HIDDEN = 7;
    public const int REASON_CARD_MASK_HIDDEN = 8;
    public const int REASON_CARD_EXPIRE_HIDDEN = 9;
    public const int REASON_CARD_NAME_HIDDEN = 10;
    public const int REASON_DRIVER_LICENSE_FORBIDDEN = 11;
    public const int REASON_RESIDENCE_PERMIT_FORBIDDEN = 12;
    public const int REASON_THIRD_PERSON_ON_DOC = 13;
    public const int REASON_PHOTOSHOP_FORBIDDEN = 14;
    public const int REASON_DOCUMENT_NOT_PROVIDED = 15;
    public const int REASON_ID_SERIAL_AND_NUM_FULLY_HIDDEN = 16;
    public const int REASON_OTHER = 100;

    /** Used in API, do not rename  */
    public const array REASONS = [
        self::REASON_LOW_QUALITY => 'Low quality',
        self::REASON_SMALL_RESOLUTION => 'Small resolution',
        self::REASON_CROPPED => 'Cropped',
        self::REASON_TOO_FAR => 'Too far',
        self::REASON_OVERLIGHTED => 'Overlighted',
        self::REASON_PASSPORT_NOT_FULL => 'Passport not full',
        self::REASON_PASSPORT_SERIAL_AND_NUM_FULLY_HIDDEN => 'Passport serial and num fully hidden',
        self::REASON_CARD_MASK_HIDDEN => 'Card mask hidden',
        self::REASON_CARD_EXPIRE_HIDDEN => 'Card expire hidden',
        self::REASON_CARD_NAME_HIDDEN => 'Card name hidden',
        self::REASON_DRIVER_LICENSE_FORBIDDEN => 'Driver license forbidden',
        self::REASON_RESIDENCE_PERMIT_FORBIDDEN => 'Residence permit forbidden',
        self::REASON_THIRD_PERSON_ON_DOC => 'Third person on doc',
        self::REASON_PHOTOSHOP_FORBIDDEN => 'Photoshop forbidden',
        self::REASON_DOCUMENT_NOT_PROVIDED => 'Document not provided',
        self::REASON_ID_SERIAL_AND_NUM_FULLY_HIDDEN => 'ID serial and num fully hidden',
        self::REASON_OTHER => 'Other', // Must be always last (for convenience)
    ];

    public const array REASONS_API_NAMES = [
        self::REASON_LOW_QUALITY => 'low_quality',
        self::REASON_SMALL_RESOLUTION => 'small_resolution',
        self::REASON_CROPPED => 'cropped',
        self::REASON_TOO_FAR => 'too_far',
        self::REASON_OVERLIGHTED => 'overlighted',
        self::REASON_PASSPORT_NOT_FULL => 'passport_not_full',
        self::REASON_PASSPORT_SERIAL_AND_NUM_FULLY_HIDDEN => 'passport_serial_and_num_fully_hidden',
        self::REASON_CARD_MASK_HIDDEN => 'card_mask_hidden',
        self::REASON_CARD_EXPIRE_HIDDEN => 'card_expire_hidden',
        self::REASON_CARD_NAME_HIDDEN => 'card_name_hidden',
        self::REASON_DRIVER_LICENSE_FORBIDDEN => 'driver_license_forbidden',
        self::REASON_RESIDENCE_PERMIT_FORBIDDEN => 'residence_permit_forbidden',
        self::REASON_THIRD_PERSON_ON_DOC => 'third_person_on_doc',
        self::REASON_PHOTOSHOP_FORBIDDEN => 'photoshop_forbidden',
        self::REASON_DOCUMENT_NOT_PROVIDED => 'document_not_provided',
        self::REASON_ID_SERIAL_AND_NUM_FULLY_HIDDEN => 'id_serial_and_num_fully_hidden',
        self::REASON_OTHER => 'other',
    ];

    public const int TYPE_PASSPORT_OR_ID_CARD = 1;
    /** @deprecated  */
    public const int TYPE_ID_CARD = 2;
    public const int TYPE_DRIVER_LICENSE = 3;
    public const int TYPE_INTERNATIONAL_PASSPORT = 4;
    public const int TYPE_ID_CARD_FRONT = 5;
    public const int TYPE_ID_CARD_BACK = 6;
    public const int TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD = 7;
    /** @deprecated  */
    public const int TYPE_SELFIE_WITH_ID_CARD = 8;
    public const int TYPE_SELFIE_WITH_DRIVER_LICENSE = 9;
    public const int TYPE_SELFIE_WITH_BANK_CARD = 10;
    public const int TYPE_BANK_STATEMENT = 11;
    public const int TYPE_BANK_SCREENSHOT = 12;
    public const int TYPE_BANK_CARD_FRONT = 13;
    public const int TYPE_BANK_CARD_BACK = 14;
    public const int TYPE_OTHER = 15;
    /** @deprecated  */
    public const int TYPE_ADDRESS_PROOF = 16;
    /** @deprecated  */
    public const int TYPE_UTILITY_BILL = 17;
    /** @deprecated  */
    public const int TYPE_CHANGE_NAME_PROOF = 18;
    /** @deprecated  */
    public const int TYPE_BIRTH_CERTIFICATE = 19;
    /** @deprecated  */
    public const int TYPE_DEATH_CERTIFICATE = 20;
    public const int TYPE_SELFIE_WITH_IDENTIFICATION_DOCUMENT_AND_BANK_CARD = 21;
    public const int TYPE_MOBILE_COMMERCE_CONTRACT = 22;
    public const int TYPE_WALLET_ACCOUNT_SCREENSHOT = 23;
    public const int TYPE_SOURCE_OF_WEALTH = 24;

    public const array TYPES = [
        self::TYPE_PASSPORT_OR_ID_CARD => 'Passport or id card',
        self::TYPE_DRIVER_LICENSE => 'Driver license',
        self::TYPE_INTERNATIONAL_PASSPORT => 'International passport',
        self::TYPE_ID_CARD_BACK => 'Id card back',
        self::TYPE_ID_CARD_FRONT => 'Id card front',
        self::TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD => 'Selfie with passport or id card',
        self::TYPE_SELFIE_WITH_DRIVER_LICENSE => 'Selfie with driver license',
        self::TYPE_SELFIE_WITH_BANK_CARD => 'Selfie with bank card',
        self::TYPE_SELFIE_WITH_IDENTIFICATION_DOCUMENT_AND_BANK_CARD => 'Selfie with identification document and bank card',
        self::TYPE_BANK_STATEMENT => 'Bank statement',
        self::TYPE_BANK_SCREENSHOT => 'Bank screenshot',
        self::TYPE_BANK_CARD_FRONT => 'Bank card front',
        self::TYPE_BANK_CARD_BACK => 'Bank card back',
        self::TYPE_MOBILE_COMMERCE_CONTRACT => 'Mobile commerce contract',
        self::TYPE_WALLET_ACCOUNT_SCREENSHOT => 'Wallet account screenshot',
        self::TYPE_SOURCE_OF_WEALTH => 'Source of Wealth',
        self::TYPE_OTHER => 'Other',
    ];

    public const array TYPES_API_NAMES = [
        self::TYPE_PASSPORT_OR_ID_CARD => 'passport_or_id_card',
        self::TYPE_DRIVER_LICENSE => 'driver_license',
        self::TYPE_INTERNATIONAL_PASSPORT => 'international_passport',
        self::TYPE_ID_CARD_BACK => 'id_card_back',
        self::TYPE_ID_CARD_FRONT => 'id_card_front',
        self::TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD => 'selfie_with_passport_or_id_card',
        self::TYPE_SELFIE_WITH_DRIVER_LICENSE => 'selfie_with_driver_license',
        self::TYPE_SELFIE_WITH_BANK_CARD => 'selfie_with_bank_card',
        self::TYPE_SELFIE_WITH_IDENTIFICATION_DOCUMENT_AND_BANK_CARD => 'selfie_with_identification_document_and_bank_card',
        self::TYPE_BANK_STATEMENT => 'bank_statement',
        self::TYPE_BANK_SCREENSHOT => 'bank_screenshot',
        self::TYPE_BANK_CARD_FRONT => 'bank_card_front',
        self::TYPE_BANK_CARD_BACK => 'bank_card_back',
        self::TYPE_MOBILE_COMMERCE_CONTRACT => 'mobile_commerce_contract',
        self::TYPE_WALLET_ACCOUNT_SCREENSHOT => 'wallet_account_screenshot',
        self::TYPE_SOURCE_OF_WEALTH => 'source_of_wealth',
        self::TYPE_OTHER => 'other',
    ];

    public const int FILES_LIMIT_DEFAULT = 1;

    public const array FILES_LIMIT_BY_TYPE = [
        self::TYPE_PASSPORT_OR_ID_CARD => 2,
        self::TYPE_DRIVER_LICENSE => 2,
        self::TYPE_OTHER => 10,
    ];

    public const int SOURCE_ANALYTICS = 1;
    public const int SOURCE_API_S2P = 2;
    public const int SOURCE_PRODUCT = 3;

    public const array SOURCES = [
        self::SOURCE_ANALYTICS => 'Analytics',
        self::SOURCE_API_S2P => 'Api S2P',
        self::SOURCE_PRODUCT => 'Product',
    ];

    public const int ACTION_STATUS_CHANGE = 1;
    public const int ACTION_DOC_UPLOAD = 2;
    public const int ACTION_DOC_DELETE = 3;
    public const int ACTION_DOC_RESTORE = 4;

    public const array ACTIONS = [
        self::ACTION_STATUS_CHANGE => 'Status change',
        self::ACTION_DOC_UPLOAD => 'Document upload',
        self::ACTION_DOC_DELETE => 'Document delete',
        self::ACTION_DOC_RESTORE => 'Document restore',
    ];

    public const string DOC_DETAIL_FIELD_ID = 'id';
    public const string DOC_DETAIL_FIELD_CREATED_AT = 'created_at';
    public const string DOC_DETAIL_FIELD_DELETED_AT = 'deleted_at';
    public const string DOC_DETAIL_FIELD_TAGS = 'tags';

    public const string DOC_DETAIL_FIELD_TYPE = 'type';
    public const string DOC_DETAIL_FIELD_TRANSACTION_ID = 'transactionId';
    public const string DOC_DETAIL_FIELD_REQUISITE_HINT = 'requisiteHint';
    public const string DOC_DETAIL_FIELD_REQUISITE = 'requisite';
    public const string DOC_DETAIL_FIELD_COMMENT = 'comment';
    public const string DOC_DETAIL_FIELD_REASONS = 'reasons';

    /** Only for external API, not in DB */
    public const string DOC_DETAIL_FIELD_API_FILES_LIMIT = 'files_limit';
    public const string DOC_DETAIL_FIELD_API_TRANSACTION_ID = 'transaction_id';

    public int $id;
    #[IdValidator]
    public int $site_id;
    #[BigIdValidator]
    public int $user_id;
    #[IntInArrayValidator(UserKyc::KYC_STATUSES)]
    public int $kyc_status;
    #[JsonArrayOfObjectsValidator]
    public ?array $details;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[IntValidator]
    public int $created_by;
    #[IntInArrayValidator(self::SOURCES)]
    public int $source;
    #[IntInArrayValidator(self::ACTIONS)]
    public int $action = self::ACTION_STATUS_CHANGE;
    #[PgArrayValidator([IdValidator::class], false)]
    public ?PgArray $doc_ids = null;

    public static function userDocumentDetailOperations(UserDocument $doc, int $action): array
    {
        $base = [
            self::DOC_DETAIL_FIELD_TAGS => $doc->tags->toArray(),
            self::DOC_DETAIL_FIELD_ID => $doc->id,
        ];

        return match ($action) {
            self::ACTION_DOC_UPLOAD => $base,
            self::ACTION_DOC_DELETE => [
                ...$base,
                self::DOC_DETAIL_FIELD_CREATED_AT => $doc->created_at->format('Y-m-d H:i:s'),
            ],
            self::ACTION_DOC_RESTORE => [
                ...$base,
                self::DOC_DETAIL_FIELD_DELETED_AT => $doc->deleted_at->format('Y-m-d H:i:s'),
            ]
        };
    }

    public static function defaultDetails(): array
    {
        return array_map(static fn ($t) => [
            'type' => $t,
            'reasons' => [self::REASON_DOCUMENT_NOT_PROVIDED],
        ], [...KycDetailsRequestModel::DOC_TYPES_DEFAULT, self::TYPE_OTHER]);
    }
}
