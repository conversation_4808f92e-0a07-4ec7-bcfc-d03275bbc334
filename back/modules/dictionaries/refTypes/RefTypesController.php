<?php

declare(strict_types=1);

namespace app\back\modules\dictionaries\refTypes;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\BaseAuthAccess;
use app\back\components\Request;
use app\back\components\SessionMessages;
use app\back\components\WebController;

#[AccessCheckPage]
class RefTypesController extends WebController
{
    public function actionData(RefTypesListForm $form, Request $request): array
    {
        return $form->validateAndResponse($request->json());
    }

    public function actionCreateForm(RefTypesCreateForm $form): array
    {
        return $form->response();
    }

    public function actionCreate(RefTypesCreateForm $form, Request $request, BaseAuthAccess $auth): void
    {
        $data = $request->json();
        $form->validateOrException($data);
        $form->create($auth->employeeId());
        $this->bl()->create($data);
    }

    public function actionDelete(RefTypesDeleteForm $form, Request $request, SessionMessages $messages): void
    {
        $data = $request->json();
        $form->validateOrException($data);
        $form->delete();
        $this->bl()->delete($data);
        $messages->success('Successfully deleted');
    }

    public function actionRefresh(Request $request, RefTypesRefreshForm $form): void
    {
        $form->validateOrException($request->json());
        $form->save();
    }

    public function actionCount(RefTypesListForm $form, Request $request, SessionMessages $messages)
    {
        $form->validateOrException($request->json());
        $messages->info("Refcodes count by rule '{$form->value}': {$form->refcodesCount()}");
    }
}
