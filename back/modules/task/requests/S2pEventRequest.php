<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\components\helpers\Arr;
use app\back\components\parsers\JsonParser;

class S2pEventRequest extends BaseRequest
{
    use RequestWithHttpClient;
    use RequestWithParserAndConverter;

    public string $from;
    public string $to;
    public string $authKey;
    public string $path;

    protected function parserConfig(): string|array
    {
        return JsonParser::class;
    }

    protected function params(): array
    {
        return [
            'from' => strtotime($this->from) * 1000, //with milliseconds
            'to' => strtotime($this->to) * 1000,
        ];
    }

    protected function fetchData(): iterable
    {
        $client = $this->createHttpClient();

        $url = $this->addQueryParams($this->buildUrl(), $this->params());

        $data = Arr::fromIterable($this->responseToData($client->get($url, [
            'auth_bearer' => $this->authKey,
        ]), $this->path));

        foreach (Arr::getValueByPath($data, $this->path) as $datum) {
            yield $datum;
        }
    }
}
