<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\entities\User;
use app\back\entities\UserTransaction;
use app\back\modules\task\BaseTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UsersStatusesPaidTask extends BaseTask
{
    use UsersStatusesUpdateTrait;

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly TaskSiteIdResolver $siteIdResolver,
        private readonly Users $usersRepo,
    ) {
    }

    public function process(): void
    {
        $this->freeToPaid();
    }

    private function freeToPaid(): void
    {
        $siteId = $this->siteIdResolver->siteId();

        $affected = (new Query($this->db))
            ->select([
                'us.site_id',
                'us.user_id',
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->innerJoin(['us' => UserTransactions::TABLE_NAME], 'us.site_id = u.site_id AND us.user_id = u.user_id')
            ->where([
                'AND',
                [
                    'us.site_id' => $siteId,
                    'us.status' => UserTransaction::STATUS_SUCCESS,
                    'us.op_id' => UserTransaction::OP_IN,
                    'u.status' => User::STATUS_FREE,
                ],
                ['>=', 'us.updated_at', $this->from],
                ['<', 'us.updated_at', $this->to],
                ['IS DISTINCT FROM', 'u.is_manual_status', true],
            ])
            ->groupBy([
                'us.site_id',
                'us.user_id',
            ])
            ->all();

        // First dep of free user
        $this->updateUsersStatus($affected, User::STATUS_PAID);
    }
}
