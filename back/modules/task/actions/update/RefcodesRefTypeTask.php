<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Db;
use app\back\entities\RefTypeRule;
use app\back\modules\task\BaseTask;
use app\back\repositories\Refcodes;
use app\back\repositories\RefTypeRules;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class RefcodesRefTypeTask extends BaseTask
{
    private ?int $refTypeRuleId = null;

    public function __construct(
        private readonly ConnectionInterface $db,
    ) {
    }

    public function process(): void
    {
        if (isset($this->subId)) {
            if (!is_numeric($this->subId)) {
                throw new \InvalidArgumentException("Ref type rule id must be integer");
            }

            $this->refTypeRuleId = (int) $this->subId;
        }

        $case = new Expression($this->getCase());

        $this->totalRows = $this->affectedRows = $this->db->createCommand()->update(Refcodes::TABLE_NAME, [
            'ref_type' => $case,
            'updated_at' => new Expression('NOW()'),
        ], [
            'AND',
            ['>=', 'created_at', $this->from],
            ['<', 'created_at', $this->to],
            ['IS DISTINCT FROM', 'ref_type', $case]
        ])->execute();
    }

    private function getCase(): string
    {
        $rules = (new Query($this->db))
            ->select([
                'ref_type',
                'value_operator',
                'value' => 'LOWER(value)',
            ])
            ->from(RefTypeRules::TABLE_NAME)
            ->andFilterWhere(['id' => $this->refTypeRuleId])
            ->all();

        $cases = [];

        $rulesGroups = Arr::groupBy($rules, ['ref_type', 'value_operator'], null, 'value');

        foreach ($rulesGroups as $refType => $group) {
            foreach ($group as $operator => $values) {
                $regexp = $this->db->quoteValue(Db::prepareDbEnumRegex($values, $operator === RefTypeRule::OPERATOR_PREFIX));
                $table = Refcodes::TABLE_NAME;
                $cases[] = "WHEN $table.code ~* $regexp THEN $refType";
            }
        }
        $cases = implode(' ', $cases);

        return "CASE $cases END";
    }
}
