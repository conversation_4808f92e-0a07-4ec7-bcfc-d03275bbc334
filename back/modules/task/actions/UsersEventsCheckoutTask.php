<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\Arr;
use app\back\entities\UserEventCheckout;
use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\BaseRepository;
use app\back\repositories\DbLargeDictionary;
use app\back\repositories\UserEventCheckouts;
use app\back\repositories\Useragents;

class UsersEventsCheckoutTask extends ImportTask
{
    use TaskWithFromToRequest;

    public const array EVENT_TYPES = [
        'cashier_init' => UserEventCheckout::EVENT_TYPE_INIT,
        'open_cashier_url_started' => UserEventCheckout::EVENT_TYPE_URL_STARTED,
        'cashier_url_received' => UserEventCheckout::EVENT_TYPE_URL_RECEIVED,
        'cashier_url_rejected' => UserEventCheckout::EVENT_TYPE_URL_REJECTED,
        'cashier_iframe_loaded_and_status_open' => UserEventCheckout::EVENT_TYPE_IFRAME_LOADED,
        'appbridge_cashier_url_open' => UserEventCheckout::EVENT_TYPE_APPBRIDGE_URL_OPEN,
        'appbridge_cashier_url_timeout' => UserEventCheckout::EVENT_TYPE_APPBRIDGE_URL_TIMEOUT,
    ];

    private readonly DbLargeDictionary $useragentsDict;

    public function __construct(
        private readonly UserEventCheckouts $s2pFrontEventsRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
        private readonly Useragents $useragentsRepo,
    ) {
        $this->useragentsDict = $useragentsRepo->createDictionary();
    }

    protected function beforeFind(array &$row): bool
    {
        $row['site_id'] = $this->siteIdResolver->siteId();

        $row['event_type'] = self::EVENT_TYPES[$row['event_type_name']] ?? null;
        if ($row['event_type'] === null) {
            $this->log->error("Unknown event_type_name: " . $row['event_type_name']);
        }

        if (!empty($row['useragent'])) {
            $row['useragent_id'] = $this->useragentsDict->getIdByName($row['useragent']);
        }
        unset($row['useragent']);

        if (mb_strlen((string) $row['context_id']) > UserEventCheckout::MAX_CONTEXT_ID_LENGTH) {
            // Skip context like
            // 1) /player/profile-cashier/mc1de215810-1bdf-4b38-a3a1-32dd2
            // 2) https://hpp-eu1.7slots.store/app/CTA4cdd7bc0f189d49f382d8ce96bbc0?lang=tr&merchantReference=index67.app&bg=black_rd
            return false;
        }

        $row['event_id'] = md5(implode('|', [$row['site_id'], $row['client_created_at'], $row['product_open_id'], $row['event_type']]));

        return true;
    }

    protected function repository(): BaseRepository
    {
        return $this->s2pFrontEventsRepo;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        Arr::removeDuplicatesByColumns($rows, ['event_id']);

        return $repository->batchUpsert($rows);
    }
}
