<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\UuidHelper;
use app\back\entities\UserDocument;
use app\back\modules\task\requests\exceptions\AuthException;
use app\back\modules\task\requests\exceptions\ClientException;
use app\back\modules\task\TaskWithAdditionalRequests;
use app\back\repositories\BaseRepository;

class UsersDocumentsSumSubTask extends BaseUsersDocumentsTask
{
    use TaskWithAdditionalRequests;

    private const int DOCUMENT_TYPE_SUM_SUB = 5;

    public const int ADDITIONAL_REQUEST_YS_SIGNATURE = 1;
    public const int ADDITIONAL_REQUEST_SUM_SUB_RESOURCES = 2;

    private const int YS_DOCUMENT_STATUS_NOT_CREATED = 0;
    private const int YS_DOCUMENT_STATUS_NEED_SENT_REQUEST = 1;
    private const int YS_DOCUMENT_STATUS_REQUEST_SEND = 2;
    private const int YS_DOCUMENT_STATUS_DOCUMENTS_RECEIVED = 3;
    private const int YS_DOCUMENT_STATUS_DOCUMENTS_APPROVED = 4;
    private const int YS_DOCUMENT_STATUS_CANCELED = 5;
    private const int YS_DOCUMENT_STATUS_IN_PROGRESS = 6;
    private const int YS_DOCUMENT_STATUS_EXPIRED = 7;

    private const array YS_SUCCESS_STATUSES = [self::YS_DOCUMENT_STATUS_CANCELED, self::YS_DOCUMENT_STATUS_DOCUMENTS_APPROVED];

    protected function beforeFind(array &$row): bool
    {
        if ((int)$row['type'] !== self::DOCUMENT_TYPE_SUM_SUB) {
            return false;
        }
        unset($row['type']);

        if (!in_array($row['external_status'], self::YS_SUCCESS_STATUSES)) {
            return false;
        }

        parent::beforeFind($row);

        $row['external_id'] = UuidHelper::cast($row['external_id']);
        $row['tags'] = [UserDocument::TAG_UNKNOWN];
        return true;
    }

    protected function extractDocRows(array $row): iterable
    {
        $headersRequest = $this->createAdditionalRequestByType(
            self::ADDITIONAL_REQUEST_YS_SIGNATURE,
            [
                'documentRequestId' => $row['original_name'],
                'host' => $this->request['host'],
                'authKey' => $this->request['authKey']
            ]
        );

        foreach ($headersRequest->finalData() as $documentRequestRow) {
            $row['external_id'] = UuidHelper::cast(basename($documentRequestRow['url']));
            $row['original_name'] = $documentRequestRow['url'];
            $row['request_params'] = $documentRequestRow;
            yield $row;
        }
    }

    protected function importRow(BaseRepository $repository, array $row, ?string $docBlob = null, bool $isBroken = false): int
    {
        $rowsCount = 0;
        $sumSubRequest = $this->createAdditionalRequestByType(self::ADDITIONAL_REQUEST_SUM_SUB_RESOURCES, $row['request_params']);
        unset($row['request_params']);
        try {
            foreach ($sumSubRequest->finalData() as $document) {
                if (!in_array($document['content_type'], self::MIME_TYPES_ALLOWED, true)) {
                    continue;
                }

                $row['filename'] = uniqid('', false) . '.jpg';
                $rowsCount += parent::importRow($repository, $row, $document['binary']);
            }
        } catch (AuthException | ClientException $e) {
            // sub sub throws 403 when document have status: Image file for that applicant action is being processed at the moment
            $this->log->debug($e->getMessage());
        }

        return $rowsCount;
    }

    protected function getDownloadLink(array $row): string
    {
        return '';
    }
}
