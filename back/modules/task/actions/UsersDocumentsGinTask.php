<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\DateHelper;
use app\back\entities\UserDocument;
use app\back\modules\task\requests\GinFromToRequest;

class UsersDocumentsGinTask extends BaseUsersDocumentsTask
{
    private const string STATUS_UNDEFINED = 'undefined';
    private const string STATUS_APPROVED = 'approved';
    private const string STATUS_DECLINED = 'declined';

    private const array STATUSES_MAP = [
        self::STATUS_UNDEFINED => UserDocument::STATUS_UNVERIFIED,
        self::STATUS_DECLINED => UserDocument::STATUS_DECLINED,
        self::STATUS_APPROVED => UserDocument::STATUS_VALID,
    ];

    private const string TYPE_ADDRESS_PROOF = 'address_proof';

    private const array TYPES_MAP = [
        'id_pass' => [UserDocument::TAG_ID_CARD],
        'passport' => [UserDocument::TAG_PASSPORT],
        'driving_licence' => [UserDocument::TAG_DIVING_LICENSE],
        'payment_method' => [UserDocument::TAG_BANK_CARD],
        'selfie_with_id' => [UserDocument::TAG_SELFIE],
        'national_identity_card' => [UserDocument::TAG_PASSPORT],
    ];

    public string $dateFormat;

    protected function batchSize(): int
    {
        return 200;
    }

    protected function getDownloadLink(array $row): string
    {
        $signature = GinFromToRequest::urlAndSortedParamsHash($row['original_name'], $this->request['authKey'], []);
        return "{$this->request['host']}/{$row['original_name']}/?signature=$signature";
    }

    protected function beforeFind(array &$row): bool
    {
        parent::beforeFind($row);

        $row['original_name'] = "{$this->request['url']}/brands/{$row['brand_id']}/players/{$row['user_id']}/images/{$row['external_id']}";
        unset($row['brand_id']);

        $fileInfo = "File {$row['original_name']} type: {$row['type']}, mime: {$row['mime_type']}, status: {$row['status']}";

        if (!empty($row['user_id'])) {
            $row['user_id'] = (int)$row['user_id'];
        }

        $row['tags'] = self::TYPES_MAP[$row['type']] ?? [$row['type']];
        $row['external_created_at'] = DateHelper::parseDate($row['external_created_at'], $this->dateFormat);

        if ($row['type'] === self::TYPE_ADDRESS_PROOF) {
            $this->log->debug("Skip address proof type. {$fileInfo}");
            return false;
        }
        unset($row['type']);

        if ($row['status'] === self::STATUS_DECLINED) {
            return false;
        }

        $row['status'] = self::STATUSES_MAP[$row['status']] ?? null;

        if (!in_array($row['mime_type'], self::MIME_TYPES_ALLOWED, true)) {
            $this->log->debug("Skip invalid mime type. {$fileInfo}");
            return false;
        }
        unset($row['mime_type']);

        return true;
    }
}
