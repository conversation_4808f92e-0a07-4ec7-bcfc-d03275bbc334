<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\send;

use app\back\components\Initializable;
use app\back\entities\UserMetric;
use app\back\modules\reports\reports\UsersInfo\UsersInfoConfig;

class VipProbabilityTask extends BaseSendTask
{
    public string $contact;
    public string $titlePostfix;
    public array $countries;
    public array $siteIds;
    protected bool $sendIfEmptyContent = false;

    private string $datetimeFrom;
    private string $datetimeTo;
    private readonly UsersInfoConfig $report;

    #[Initializable]
    final public function init(UsersInfoConfig $report): void
    {
        $this->report = $report;

        $this->datetimeFrom = date('Y-m-d H:i', $this->fromTime);
        $this->datetimeTo =  date('Y-m-d H:i', strtotime($this->to));
    }

    protected function getContactName(): string
    {
        return $this->contact;
    }

    public function getSubject(): string
    {
        return "VIP probability $this->titlePostfix ($this->datetimeFrom - $this->datetimeTo)";
    }

    public function getContent(): string
    {
        ['data' => $data, 'columns' => $cols] =
            $this->report->loadAndValidateOrException([
                'isHtmlVersion' => true,
                'columns' => [
                    'site_id',
                    'player_link',
                    'site_user',
                    'country',
                    'personal_manager',
                    'vip_probability'
                ],
                'filters' => [
                    ['site_id', $this->siteIds],
                    ['country', $this->countries, 'IN'],
                    ['vip_classification_at', $this->datetimeFrom, '>='],
                    ['vip_classification_at', $this->datetimeTo, '<'],
                    ['vip_probability', UserMetric::MIN_VIP_PERCENT_FOR_SHOW, '>'],
                ],
            ])
            ->dataAndColumns();

        if (!$data) {
            return '';
        }

        return $this->table($data, $cols, $this->getSubject());
    }
}
