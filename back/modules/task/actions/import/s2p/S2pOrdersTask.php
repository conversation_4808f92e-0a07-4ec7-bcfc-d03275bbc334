<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import\s2p;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Json;
use app\back\components\Initializable;
use app\back\components\services\GeoIp;
use app\back\components\UserContactAdder;
use app\back\entities\Requisite;
use app\back\entities\S2pOrder;
use app\back\repositories\BaseRepository;
use app\back\repositories\Cities;
use app\back\repositories\DbLargeDictionary;
use app\back\repositories\Hosts;
use app\back\repositories\Rates;
use app\back\repositories\Refcodes;
use app\back\repositories\S2pMids;
use app\back\repositories\S2pOrders;
use app\back\repositories\S2pPayClasses;
use app\back\repositories\S2pPayMethods;
use app\back\repositories\S2pPaySystems;
use app\back\repositories\S2pProjects;
use app\back\repositories\S2pStatuses;
use app\back\repositories\Useragents;

class S2pOrdersTask extends S2pBaseFromToTask
{
    private const array DEVICES = [
        'desktop' => S2pOrder::DEVICE_DESKTOP,
        'mobile' => S2pOrder::DEVICE_MOBILE,
        'mob' => S2pOrder::DEVICE_MOBILE,
        'web' => S2pOrder::DEVICE_WEB,
    ];

    private const array PAY_SYSTEM_TYPES = [
        'common' => S2pOrder::PAY_SYSTEM_TYPE_COMMON,
        'internal_out' => S2pOrder::PAY_SYSTEM_TYPE_INTERNAL,
        'merchant_out' => S2pOrder::PAY_SYSTEM_TYPE_MERCHANT,
        'api_out' => S2pOrder::PAY_SYSTEM_TYPE_API,
    ];

    private const array SKIP_WALLETS_TYPES = [
        "alfaclick", // contains not validated users inputs like "alfa", "987", "A"
    ];

    public int $instanceId;

    private readonly DbLargeDictionary $useragentsDict;
    private readonly UserContactAdder $contactAdder;
    private readonly DbLargeDictionary $hostsDict;

    private array $newRequisiteTypes = [
        'payer' => 'ignored',
        'billing_address' => 'ignored',
        'bank_transfer' => 'ignored',
        'cup' => 'ignored', // if order not success or requisite is not is_payment
    ];

    public function __construct(
        private readonly S2pOrders $s2pOrdersRepo,
        private readonly S2pProjects $s2pProjectsRepo,
        private readonly S2pPaySystems $s2pPaySystemsRepo,
        private readonly S2pPayClasses $s2pPayClassesRepo,
        private readonly S2pMids $s2pMidsRepo,
        private readonly S2pPayMethods $s2pPayMethodsRepo,
        private readonly S2pStatuses $s2pStatusesRepo,
        private readonly Refcodes $refcodesRepo,
        private readonly GeoIp $geoIp,
        private readonly Cities $citiesRepo,
        private readonly Rates $ratesRepo,
        private readonly Hosts $hostsRepo,
        Useragents $useragentsRepo,
    ) {
        $this->useragentsDict = $useragentsRepo->createDictionary();
        $this->hostsDict = new DbLargeDictionary(Hosts::class, $hostsRepo->db);
    }

    #[Initializable]
    final public function initUserContactAdder(UserContactAdder $userContactAdder): void
    {
        $userContactAdder->setSkipEvents($this->skipEvents);
        $this->contactAdder = $userContactAdder;
    }

    protected function batchSize(): int
    {
        return $this->mode === 'nobatch' ? 1 : parent::batchSize();
    }

    protected function repository(): BaseRepository
    {
        return $this->s2pOrdersRepo;
    }

    protected function beforeFind(array &$row): bool
    {
        $row['instance_id'] = $this->instanceId;

        $row['project_id'] = $this->s2pProjectsRepo->getIdByName($row['project']);
        unset($row['project']);

        $row['site_id'] = $this->s2pProjectsRepo->getSiteIdByProjectId($row['project_id']);
        $row['version'] = S2pOrder::VERSION_3;
        $row['mid_id'] = $this->s2pMidsRepo->getIdByName($row['mid_name']);
        unset($row['mid_name']);

        $row['pay_type'] = S2pOrder::getPayTypeIdByName($row['pay_type_name']);
        unset($row['pay_type_name']);
        $row['pay_sys_id'] = $this->s2pPaySystemsRepo->getIdByName($row['payment_system']);

        $row['pay_sys_type'] = self::PAY_SYSTEM_TYPES[$row['payment_system_type_name']] ?? null;
        if ($row['pay_sys_type'] === null) {
            $this->log->warning("New pay sys type: {$row['payment_system_type_name']}");
        }
        unset($row['payment_system_type_name']);

        if (!empty($row['comment_arr'])) {
            $row['comment'] = implode(', ', array_column($row['comment_arr'], 'comment'));
        }
        unset($row['comment_arr']);

        if (!empty($row['comment_ps_arr'])) {
            $row['comment_ps'] = implode(', ', array_column($row['comment_ps_arr'], 'comment'));
        }
        unset($row['comment_ps_arr']);

        $row['pay_class_id'] = $this->s2pPayClassesRepo->getIdByName($row['pay_class']);
        unset($row['pay_class']);

        $row['device'] = $this->getDeviceId($row['device'] ?? null, $row['device_type'] ?? null);
        unset($row['device_type']);

        $this->setStatus($row);

        if (!empty($row['requisites'])) {
            $reqs = $row['requisites'];

            if (!empty($reqs['project_user']['name'])) {
                $row['user_id'] = $this->extractUserId($reqs['project_user']['name']);
                unset($row['requisites']['project_user']);
            }

            if (!empty($reqs['ip']['ip'])) {
                $row['ip'] = $reqs['ip']['ip'];
                $row['country'] = $this->geoIp->getCountryCode($row['ip']);
                $row['city_id'] = $this->citiesRepo->getIdByIp($this->geoIp, $row['ip']);
                unset($row['requisites']['ip']);
            }

            $this->setRequisite($row);
        }
        unset($row['payment_system'], $row['requisites']);

        if (empty($row['requisite'])) {
            $row['requisite'] = null;
            $row['requisite_type'] = null;
        }

        if (!empty($row['status_detail']['code'])) {
            $statusDetail = $row['status_detail'];
            $this->s2pStatusesRepo->checkIdNameExists((int) $statusDetail['code'], $statusDetail['message']);
            $row['status_id'] = $statusDetail['code'];
        }
        unset($row['status_detail']);

        if (!empty($row['pay_method'])) {
            $row['pay_method_id'] = $this->s2pPayMethodsRepo->getIdByName($row['pay_method']);
        }
        unset($row['pay_method']);

        $row['summ'] = $this->ratesRepo->convert($row['client_original_amount'], $row['client_original_currency'], 'USD', $row['date_created']);
        $row['summ_rub'] = $this->ratesRepo->convert($row['client_original_amount'], $row['client_original_currency'], 'RUB', $row['date_created']);
        $row['summ_eur'] = $this->ratesRepo->convert($row['client_original_amount'], $row['client_original_currency'], 'EUR', $row['date_created']);

        if (!empty($row['payer_identity'])) {
            $fields = [
                'requisite' => 'trust_requisite',
                'login' => 'trust_login',
                'user' => 'trust_user',
            ];

            foreach ($fields as $part => $field) {
                if (!empty($row['payer_identity'][$part])) {
                    $row[$field] = S2pOrder::getTrustLevelIdByName($row['payer_identity'][$part]);

                    if ($row[$field] === null) {
                        $this->log->warning('Unknown trust level: ' . $row['payer_identity'][$part]);
                    }
                }
            }

            if ($row['payer_identity']['score'] !== '') {
                $row['trust_score'] = $row['payer_identity']['score'];
            }
        }
        unset($row['payer_identity']);

        if (!empty($row['refcode'])) {
            $row['refcode_id'] = $this->refcodesRepo->getIdByCode($row['refcode']);
        }
        unset($row['refcode']);

        if (!empty($row['useragent'])) {
            $row['useragent_id'] = $this->useragentsDict->getIdByName($row['useragent']);
        }
        unset($row['useragent']);

        if (!empty($row['host'])) {
            $host = parse_url($row['host'], PHP_URL_HOST);
            if ($host !== false) {
                if (!empty($row['site_id'])) {
                    $row['host_id'] = $this->hostsRepo->getIdByHostAndSite($host, (int)$row['site_id']);
                } else {
                    $row['host_id'] = $this->hostsDict->getIdByName($host);
                }
            }
        }

        $this->tryAddPhone($row);

        return parent::beforeFind($row);
    }

    // Set requisite, requisite_type and card_holder(if exists) fields based on $row['requisites']
    private function setRequisite(&$row): void
    {
        $priorityRequisites = Arr::sortByKeys($row['requisites'], ['ccard', 'pan', 'card_masked', 'wallet', 'cup', 'phone', 'email', 'iban']);
        uasort($priorityRequisites, static fn($a, $b) => $b['is_payment'] <=> $a['is_payment']);

        foreach ($priorityRequisites as $type => $details) {
            $isTrustyRequisite = $details['is_payment'] && $row['status'] === S2pOrder::STATUS_SUCCESS;
            if (($type === 'ccard' || $type === 'pan') && !empty($details['pan'])) {
                $row['requisite_type'] = S2pOrder::REQUISITE_TYPE_CARD;

                $row['requisite'] = $details['pan'];

                if (!empty($details['expiry_year']) && !empty($details['expiry_month'])) {
                    $year = $details['expiry_year'];
                    $month = $details['expiry_month'];
                    $row['requisite'] .= "/$month/$year";
                }

                if (!empty($details['holder_name'])) {
                    $row['card_holder'] = $details['holder_name'];
                }
            } elseif ($type === 'card_masked' && !empty($details['mask'])) {
                $row['requisite_type'] = S2pOrder::REQUISITE_TYPE_CARD_NUMBER;
                $row['requisite'] = strtr($details['mask'], ['X' => '*', ' ' => '']); //425634XXXXXX7012 => 425634******7012
            } elseif ($type === 'wallet' && !empty($details['value'])) {
                // skip not unique not verifiable wallets
                if (in_array($details['type'], self::SKIP_WALLETS_TYPES, true)) {
                    continue;
                }
                $row['requisite_type'] = $details['type'] === 'webmoney' ? S2pOrder::REQUISITE_TYPE_WALLET_WEBMONEY : S2pOrder::REQUISITE_TYPE_WALLET;
                $row['requisite'] = $details['value'];
            } elseif ($type === 'phone' && !empty($details['number'])) {
                $row['requisite_type'] = S2pOrder::REQUISITE_TYPE_PHONE;
                $row['requisite'] = $details['number'];
            } elseif ($type === 'email' && (!empty($details['value']) || !empty($details['email']))) {
                $row['requisite_type'] = S2pOrder::REQUISITE_TYPE_EMAIL;
                $row['requisite'] = $details['value'] ?? $details['email'];
            } elseif ($type === 'iban' && !empty($details['iban'])) {
                $row['requisite_type'] = S2pOrder::REQUISITE_TYPE_IBAN;
                $row['requisite'] = $details['iban'];
            } elseif ($type === 'cup' && !empty($details['bank_account_number']) && $isTrustyRequisite) {
                // use only $isTrustyRequisite cup because otherwise may contain trash like "000"
                $row['requisite_type'] = S2pOrder::REQUISITE_TYPE_BANK_ACCOUNT;
                $row['requisite'] = Requisite::bankAccountUniq('bn', $row['payment_system'], $details['bank_account_number']);
            } elseif ($type === 'bank_transfer' && !empty($details['value']) && $isTrustyRequisite) {
                if (!Requisite::isIBAN($details['value'])) {
                    $this->log->notice("Skip bank_transfer requisite is not IBAN {$row['id']} {$row['pay_method']}: {$details['value']}");
                    continue;
                }
                $row['requisite_type'] = S2pOrder::REQUISITE_TYPE_IBAN;
                $row['requisite'] = strtolower($details['value']);
            } elseif ($isTrustyRequisite && !empty($details['value']) && $type !== 'voucher') {
                $msg = "Unprocessable 'is_payment' requisite '$type' from {$row['id']} requisites: {$row['pay_method']}" . Json::encode($row['requisites'], JSON_PRETTY_PRINT);
                // todo always error after fix bank_account_number by s2p
                if ($type === 'cup' && $details['bank_account_number'] === '') {
                    $this->log->warning($msg);
                } else {
                    $this->log->error($msg);
                }
            } elseif (!array_key_exists($type, $this->newRequisiteTypes)) {
                $this->newRequisiteTypes[$type] = $details;
                $this->log->notice("New requisite type: $type. Details: " . Json::encode($details));
            }

            if (!empty($row['requisite'])) {
                $requisiteTypeGlobal = Requisite::getTypeIdByS2pOrderType($row['requisite_type']);
                $row['requisite'] = Requisite::normalizeRequisite($row['requisite'], $requisiteTypeGlobal);
                $row['is_payment_requisite'] = $details['is_payment'];

                if ($requisiteTypeGlobal === Requisite::TYPE_CARD) {
                    if (!empty($details['token_v2'])) {
                        $row['requisite_token'] = strtolower(substr($details['token_v2'], 0, 3));
                    } else {
                        $this->log->warning("Empty token_v2: {$row['id']}: " . Json::encode($details));
                    }
                }

                break;
            }
        }
    }

    private function getDeviceId(?string ...$devices): ?int
    {
        $devices = array_values(array_filter(array_map(static function ($d) {
            $d = trim($d ?? '');
            return empty($d) ? null : $d;
        }, $devices)));

        if (empty($devices)) {
            return null;
        }

        $result = static::DEVICES[$devices[0]] ?? null;

        if ($result === null) {
            $this->log->warning("Unknown device: $devices[0]");
            return null;
        }

        return $result;
    }

    private function setStatus(&$row): void
    {
        $key = array_search($row['status'], S2pOrder::STATUSES, true);

        if ($key === false) {
            $this->log->warning("Invalid status: {$row['status']}");
            $key = null;
        }

        $row['status'] = $key;
    }

    private function tryAddPhone(array $row): void
    {
        if (empty($row['requisites']['phone']['number'])) {
            return;
        }

        if (empty($row['site_id']) || empty($row['user_id'])) {
            return;
        }

        if ($row['requisites']['phone']['number'] !== $row['requisite']) {
            return;
        }

        $phone = $row['requisites']['phone']['number'];

        if ($phone === '+000000000000000') {
            return;
        }

        $this->contactAdder->secondaryPhoneFromS2pOrder($row['site_id'], $row['user_id'], $phone);
    }
}
