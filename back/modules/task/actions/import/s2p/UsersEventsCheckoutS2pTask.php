<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import\s2p;

use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\entities\UserEventCheckout;
use app\back\modules\task\actions\TaskWithFromToRequest;
use app\back\modules\task\ImportTask;
use app\back\repositories\BaseRepository;
use app\back\repositories\UserEventCheckouts;
use app\back\repositories\S2pPaySystems;

class UsersEventsCheckoutS2pTask extends ImportTask
{
    use TaskWithFromToRequest;

    public const array EVENT_TYPES = [
        'methods_step_opened_event' => UserEventCheckout::EVENT_TYPE_OPENED,
        'methods_step_selected_method_event' => UserEventCheckout::EVENT_TYPE_SELECTED,
        'try_to_pay' => UserEventCheckout::EVENT_TYPE_TRY_PAY,
        'payment_form_created' => UserEventCheckout::EVENT_TYPE_PAYMENT_FORM_CREATED,
        'payment_method_click' => UserEventCheckout::EVENT_TYPE_PAYMENT_METHOD_CLICK,
    ];

    public int $instanceId;

    public function __construct(
        private readonly UserEventCheckouts $s2pFrontEventsRepo,
        private readonly S2pPaySystems $s2pPaySystemsRepo,
    ) {
    }

    protected function beforeFind(array &$row): bool
    {
        $row['instance_id'] = $this->instanceId;
        $row['event_type'] = self::EVENT_TYPES[$row['event_type']] ?? null;

        $row['client_created_at'] = DateHelper::fromUnixMilliTimeToDatetimeWithMilliseconds($row['client_created_at']);
        $row['server_received_at'] = DateHelper::fromUnixMilliTimeToDatetimeWithMilliseconds($row['server_received_at']);

        if (!empty($row['event_payload']['method'])) {
            $row['pay_sys_id'] = $this->s2pPaySystemsRepo->getIdByName($row['event_payload']['method']);
        }
        unset($row['event_payload']);

        return true;
    }

    protected function repository(): BaseRepository
    {
        return $this->s2pFrontEventsRepo;
    }

    protected function getData(): iterable
    {
        $d = $this->requestFromToParams();

        $dateFrom = new \DateTimeImmutable($d['from']);
        $dateTo = new \DateTimeImmutable($d['to']);

        $dateFromPlusMin = $dateFrom->add(new \DateInterval('PT1M'));

        if (
            $dateFrom->format("Y-m-d H:i") !== $dateTo->format("Y-m-d H:i") &&
            ($dateFromPlusMin->format("Y-m-d H:i") !== $dateTo->format("Y-m-d H:i") || (int)$dateTo->format('s') !== 0)
        ) {
            throw new \InvalidArgumentException('From and To must be in range 1 minute');
        }

        return $this->createRequest($d)->finalData();
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        Arr::removeDuplicatesByColumns($rows, ['event_id']);

        return $repository->batchUpsert($rows);
    }
}
