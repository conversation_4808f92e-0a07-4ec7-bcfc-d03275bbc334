<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import\s2p;

use app\back\entities\Rate;
use app\back\entities\S2pOrder;
use app\back\entities\S2pTransaction;
use app\back\repositories\BaseRepository;
use app\back\repositories\Rates;
use app\back\repositories\S2pPayClasses;
use app\back\repositories\S2pPaySystems;
use app\back\repositories\S2pProjects;
use app\back\repositories\S2pStatuses;
use app\back\repositories\S2pTransactions;

class S2pTransactionsTask extends S2pBaseFromToTask
{
    private const array STATUSES = [
        'new' => S2pTransaction::STATUS_NEW,
        'process' => S2pTransaction::STATUS_PROCESS,
        'success' => S2pTransaction::STATUS_SUCCESS,
        'fail' => S2pTransaction::STATUS_FAIL,
        'manual' => S2pTransaction::STATUS_MANUAL,
        'handle' => S2pTransaction::STATUS_NEW,
        'skip' => S2pTransaction::STATUS_NEW,
    ];

    private const array TYPES = [
        'BILL' => S2pTransaction::TYPE_BILL,
        'REBILL' => S2pTransaction::TYPE_REBILL,
        'WITHDRAW' => S2pTransaction::TYPE_WITHDRAW,
        'ENROLLMENT' => S2pTransaction::TYPE_ENROLLMENT,
        'PURCHASE' => S2pTransaction::TYPE_PURCHASE,
        'CHARGEBACK' => S2pTransaction::TYPE_CHARGEBACK,
        'REFUND' => S2pTransaction::TYPE_REFUND,
        'CAPTURE' => S2pTransaction::TYPE_CAPTURE,
        'AUTHORIZE' => S2pTransaction::TYPE_AUTHORIZE,
        'CANCEL' => S2pTransaction::TYPE_CANCEL,
        'CHARGEBACK_REVERSAL' => S2pTransaction::TYPE_CHARGEBACK_REVERSAL,
        'REVERSAL' => S2pTransaction::TYPE_REVERSAL,
    ];

    private const array DIRS = [
        'IN' => S2pTransaction::DIR_IN,
        'OUT' => S2pTransaction::DIR_OUT,
    ];

    public int $instanceId;

    public function __construct(
        private readonly S2pTransactions $s2pTransactionsRepo,
        private readonly S2pProjects $s2pProjectsRepo,
        private readonly S2pPaySystems $s2pPaySystemsRepo,
        private readonly S2pPayClasses $s2pPayClassesRepo,
        private readonly S2pStatuses $s2pStatusesRepo,
        private readonly Rates $ratesRepo,
    ) {
    }

    protected function repository(): BaseRepository
    {
        return $this->s2pTransactionsRepo;
    }

    /*
     Array
    (
        [uuid] => Tdf4397f3722e805648d9c10152ede3a
        [invoice] => 3134258
        [type] => BILL
        [parent_uuid] =>
        [status] => fail
        [pay_direction] => IN
        [order_uuid] => Pd7dcd7970e72598d387b1b6004a5093
        [status_detail] => Array
            (
                [id] => 36
                [code] => 300
                [message] => Expired.
            )

        [project_name] => VulkanRussia
        [pay_class_code] => pay_trio
        [pay_system_code] => yandex_money_bank_cards
        [created] => 2017-04-12 14:47:16
        [modified] => 2017-04-27 07:48:16
        [success_date] =>
        [pay_system_original_amount] => 1000.0000
        [pay_system_original_code] => RUB
        [pay_system_original_amount_usd] => 17.5787
        [pay_system_original_ratio] => 0.**********
        [transaction_fee_amount] => 0.0000
        [transaction_fee_code] => RUB
        [transaction_fee_amount_usd] => 0.0000
        [transaction_fee_ratio] => 0.**********
        [mid_fee_amount] => 0.0000
        [mid_fee_code] => RUB
        [mid_fee_amount_usd] => 0.0000
        [mid_fee_ratio] => 0.**********
    )
    */
    protected function beforeFind(array &$row): bool
    {
        $row['instance_id'] = $this->instanceId;

        $row['project_id'] = $this->s2pProjectsRepo->getIdByName($row['project_name']);
        unset($row['project_name']);

        $row['site_id'] = $this->s2pProjectsRepo->getSiteIdByProjectId($row['project_id']);

        $row['pay_sys_id'] = $this->s2pPaySystemsRepo->getIdByName($row['pay_system_code']);
        unset($row['pay_system_code']);
        $row['pay_class_id'] = $this->s2pPayClassesRepo->getIdByName($row['pay_class_code']);
        unset($row['pay_class_code']);

        if (!empty($row['status_detail']['code'])) {
            $statusDetail = $row['status_detail'];
            unset($row['status_detail']);
            $this->s2pStatusesRepo->checkIdNameExists((int) $statusDetail['code'], $statusDetail['message']);
            $row['status_id'] = $statusDetail['code'];
        }

        if (!array_key_exists($row['status'], self::STATUSES)) {
            $this->log->warning("Unknown status {$row['status']}");
            unset($row['status']);
        } else {
            $row['status'] = self::STATUSES[$row['status']];
        }

        if (!array_key_exists($row['type'], self::TYPES)) {
            $this->log->warning("Unknown type {$row['type']}");
            unset($row['type']);
        } else {
            $row['type'] = self::TYPES[$row['type']];
        }

        if (!array_key_exists($row['dir'], self::DIRS)) {
            $this->log->warning("Unknown pay dir {$row['dir']}");
        } else {
            $row['dir'] = self::DIRS[$row['dir']];
        }

        $row['amount_rub'] = $this->ratesRepo->convert($row['amount'], $row['currency'], Rate::RUB, $row['created_at']);
        $row['amount_eur'] = $this->ratesRepo->convert($row['amount'], $row['currency'], Rate::EUR, $row['created_at']);
        $row['rate'] = trim($row['rate'], '.');

        $row['tr_fee_amount_rub'] = $this->ratesRepo->convert($row['tr_fee_amount'], $row['tr_fee_currency'], Rate::RUB, $row['created_at']);
        $row['tr_fee_rate'] = trim($row['tr_fee_rate'], '.');

        $row['mid_fee_amount_rub'] = $this->ratesRepo->convert($row['mid_fee_amount'], $row['mid_fee_currency'], Rate::RUB, $row['created_at']);
        $row['mid_fee_rate'] = trim($row['mid_fee_rate'], '.');

        return parent::beforeFind($row);
    }
}
