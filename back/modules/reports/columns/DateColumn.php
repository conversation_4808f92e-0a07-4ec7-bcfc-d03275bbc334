<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\validators\DateValidator;
use Yiisoft\Db\Connection\ConnectionInterface;

class DateColumn extends BaseColumn implements Operators, Selected, Filtered
{
    public string $column = 'date';
    public string $title = 'Date';
    public bool $reversedOperators = false;

    public function inputProps(): array
    {
        return [
            'type' => 'date',
        ];
    }

    public function rule(): array
    {
        return [DateValidator::class];
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        self::addOneDayAndReplaceOperatorIfOperatorLE($value, $operator);

        if ($operator === null) {
            throw new \RuntimeException("Operator is null");
        }

        $query->andWhere([$operator, $this->selectExpression($db, $query), $value]);
    }

    public function operators(): array
    {
        return $this->reversedOperators ? [Operators::LE, Operators::GE, Operators::L, Operators::G] : [Operators::GE, Operators::LE, Operators::G, Operators::L];
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }

    public static function addOneDayAndReplaceOperatorIfOperatorLE(&$value, ?string &$operator): void
    {
        // convert '<= 2020-12-15' to '< 2020-12-16'
        if ($value && preg_match('#^\d{4}-\d{2}-\d{2}$#', $value) && $operator === static::LE) {
            $value = date('Y-m-d', strtotime('+1 day', strtotime($value)));
            $operator = static::L;
        }
    }
}
