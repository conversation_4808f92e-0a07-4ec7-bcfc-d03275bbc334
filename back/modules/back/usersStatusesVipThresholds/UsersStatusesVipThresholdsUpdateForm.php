<?php

declare(strict_types=1);

namespace app\back\modules\back\usersStatusesVipThresholds;

use app\back\components\BaseAuthAccess;
use app\back\components\Form;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntervalValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\entities\UserStatusVipThreshold;
use app\back\repositories\Countries;
use app\back\repositories\UserStatusVipThresholds;

class UsersStatusesVipThresholdsUpdateForm
{
    use Form;

    #[IdValidator]
    public int $id;
    #[MoneyValidator]
    public ?string $amount = null;
    #[IntValidator(1)]
    public ?int $downMultiplier = null;
    #[BooleanValidator]
    public ?bool $isUp = null;
    #[BooleanValidator]
    public ?bool $isActive = null;
    #[StringInArrayValidator(UserStatusVipThreshold::PERIODS, true)]
    #[IntervalValidator(false)]
    public ?\DateInterval $period = null;
    #[IntInArrayValidator(UserStatusVipThreshold::CHECK_PERIODS)]
    public ?int $checkPeriod = null;
    #[StringInArrayValidator(UsersStatusesVipThresholdsAddForm::COLUMNS_DB_MAP, true)]
    public string $column;

    private UserStatusVipThreshold $threshold;

    public function __construct(
        private readonly UserStatusVipThresholds $thresholdsRepo,
        private readonly BaseAuthAccess $auth,
        private readonly Countries $countriesRepo,
    ) {
    }

    public function update(): void
    {
        /** @var UserStatusVipThreshold $threshold */
        $threshold = $this->thresholdsRepo->findOneOr404(['id' => $this->id]);
        $column = $this->column;
        $entityColumn = array_flip(UsersStatusesVipThresholdsAddForm::COLUMNS_DB_MAP)[$this->column];

        $threshold->$entityColumn = $this->$column;
        $threshold->updated_by = $this->auth->employeeId();
        $threshold->updated_at = new \DateTimeImmutable(UserStatusVipThreshold::SQL_NOW_DATETIME);

        $this->thresholdsRepo->update($threshold, [$entityColumn, 'updated_by', 'updated_at']);
    }
}
