<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\ginaf;

use app\back\components\Initializable;
use app\back\components\Request;
use app\back\components\services\FileStorage;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\entities\UserDocument;
use app\back\entities\UserWallet;
use app\back\modules\api\ApiGetMethod;
use app\back\modules\api\components\Operators;
use app\back\repositories\Countries;
use app\back\repositories\UserDocuments;
use app\back\repositories\Users;
use app\back\repositories\UserWallets;
use Yiisoft\Db\Query\Query;

class UsersDocumentsMethod extends ApiGetMethod
{
    #[Operators(Operators::EQ)]
    #[StringInArrayValidator([self::class, 'getCountries'])]
    protected array $country;

    #[Operators(Operators::EQ)]
    #[StringInArrayValidator(UserDocument::TAGS)]
    protected array $tag;

    #[Operators(Operators::EQ)]
    #[IntValidator(1, 1000)]
    protected array $limit;

    #[Operators(Operators::EQ)]
    #[IntValidator(0, 100000000)]
    protected array $offset = [];

    public function __construct(
        private readonly FileStorage $fileStorage,
        private readonly Countries $countries,
        private readonly UserWallets $userWalletsRepo
    ) {
    }

    public function run(): iterable
    {
        $request = $this->createRequest();

        $walletSubQuery = $this->userWalletsRepo->walletBalancesSubQuery('u', UserWallet::TYPE_REAL);

        $query = (new Query($this->db))
            ->select([
                'site_id' => 'ud.site_id',
                'user_id' => 'ud.user_id',
                'filename' => 'ud.filename',
                'tags' => 'array_to_json(ud.tags)',
                'locale' => 'u.locale',
                'country' => 'u.country',
                'currency' => 'uw.currency',
            ])
            ->from(['ud' => UserDocuments::TABLE_NAME])
            ->leftJoin(['u' => Users::TABLE_NAME], 'u.site_id = ud.site_id AND u.user_id = ud.user_id')
            ->join("LEFT JOIN LATERAL", ['uw' => $walletSubQuery], 'true')
            ->where('ud.external_approve_required IS NOT TRUE')
            ->orderBy(['ud.id' => SORT_ASC]);

        if (!empty($this->tag)) {
            $query->andWhere('ud.tags @> ARRAY[:tag]::VARCHAR[]', [
                ':tag' => $request->getParam('tag', null, true)
            ]);
        }

        $request->map([
            'country' => 'ud.country',
        ]);

        $request->filterParams($query);

        return $this->fetchEach($query);
    }

    public function decorator(): \Closure|bool
    {
        return fn ($row) => [
            'user_locale' => $this->localeNormalize($row['locale']),
            'user_country' => $row['country'],
            'user_currency' => $row['currency'],
            'tags' => json_decode($row['tags'], true, 512, JSON_THROW_ON_ERROR),
            'url' => $this->fileStorage->getPublicUrlByKey(UserDocument::composeStoragePath($row['site_id'], $row['user_id'], $row['filename'])),
        ];
    }

    public static function getCountries(self $method): array
    {
        return $method->countries->getNames();
    }

    protected function contentType(Request $request): string
    {
        return static::CONTENT_TYPE_JSON;
    }

    private function localeNormalize(?string $locale): ?string
    {
        if (empty($locale)) {
            return null;
        }

        $parts = explode('_', str_replace('-', '_', $locale), 2);
        if (count($parts) === 2) {
            return strtolower($parts[0]) . '_' . strtoupper($parts[1]);
        }

        return strtolower($parts[0]);
    }
}
