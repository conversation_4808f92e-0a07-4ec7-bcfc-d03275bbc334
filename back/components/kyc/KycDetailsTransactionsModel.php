<?php

declare(strict_types=1);

namespace app\back\components\kyc;

use app\back\components\helpers\Db;
use app\back\components\SecondaryConnection;
use app\back\components\SessionMessages;
use app\back\entities\CanonicalPaySySource;
use app\back\entities\CanonicalPaySystem;
use app\back\entities\UserDocumentProgress;
use app\back\repositories\CanonicalPaySySources;
use app\back\repositories\CanonicalPaySystems;
use app\back\repositories\PaySystems;
use app\back\repositories\S2pOrders;
use app\back\repositories\S2pPaySystems;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Query\Query;

class KycDetailsTransactionsModel
{
    private const array PAY_SYS_TYPE_TO_DOCS_MAP = [
        CanonicalPaySystem::CANONICAL_PS_APPLEGOOGLEPAY => [UserDocumentProgress::TYPE_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_BANK_SCREENSHOT],
        CanonicalPaySystem::CANONICAL_PS_BANKTRANSFER => [UserDocumentProgress::TYPE_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_BANK_STATEMENT, UserDocumentProgress::TYPE_BANK_SCREENSHOT],
        CanonicalPaySystem::CANONICAL_PS_CARDS => [UserDocumentProgress::TYPE_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_BANK_CARD_FRONT, UserDocumentProgress::TYPE_BANK_CARD_BACK, UserDocumentProgress::TYPE_SELFIE_WITH_IDENTIFICATION_DOCUMENT_AND_BANK_CARD],
        CanonicalPaySystem::CANONICAL_PS_CARDSP2P => [UserDocumentProgress::TYPE_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_BANK_STATEMENT, UserDocumentProgress::TYPE_BANK_SCREENSHOT],
        CanonicalPaySystem::CANONICAL_PS_CASH1 => [UserDocumentProgress::TYPE_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_OTHER],
        CanonicalPaySystem::CANONICAL_PS_CRYPTO => [UserDocumentProgress::TYPE_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_OTHER],
        CanonicalPaySystem::CANONICAL_PS_INNER => [UserDocumentProgress::TYPE_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_OTHER],
        CanonicalPaySystem::CANONICAL_PS_MOBILECOMMERCE => [UserDocumentProgress::TYPE_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_MOBILE_COMMERCE_CONTRACT],
        CanonicalPaySystem::CANONICAL_PS_WALLET => [UserDocumentProgress::TYPE_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD, UserDocumentProgress::TYPE_WALLET_ACCOUNT_SCREENSHOT],
        CanonicalPaySystem::CANONICAL_PAY_SYS_UNMAPPED => [...KycDetailsRequestModel::DOC_TYPES_DEFAULT, UserDocumentProgress::TYPE_OTHER],
    ];

    public function __construct(
        private readonly SecondaryConnection $db,
        private readonly SessionMessages $sessionMessages,
    ) {
    }

    public function detailsArrayByTransactions(int $siteId, int $userId, array $transactionOrS2pIds, bool $requisiteDependentTypesOnly = false): array
    {
        $details = [];
        foreach ($this->findCanonicalPaySystems($siteId, $userId, $transactionOrS2pIds) as $transactionOrS2pId => $paySystem) {
            foreach (self::PAY_SYS_TYPE_TO_DOCS_MAP[$paySystem] ?? self::PAY_SYS_TYPE_TO_DOCS_MAP[CanonicalPaySystem::CANONICAL_PAY_SYS_UNMAPPED] as $docType) {
                $details[] = [
                    UserDocumentProgress::DOC_DETAIL_FIELD_TYPE => $docType,
                    UserDocumentProgress::DOC_DETAIL_FIELD_TRANSACTION_ID => (string)$transactionOrS2pId,
                ];
            }
        }

        $notFoundTransactions = array_diff($transactionOrS2pIds, array_column($details, UserDocumentProgress::DOC_DETAIL_FIELD_TRANSACTION_ID));
        if (!empty($notFoundTransactions)) {
            $this->sessionMessages->error('Not fount payment data for id(s): ' . implode($notFoundTransactions));
        }

        return array_values(self::filterRequisiteIndependentDetailsDuplicates($details, $requisiteDependentTypesOnly));
    }

    private function findCanonicalPaySystems(int $siteId, int $userId, array $transactionOrS2pIds): array
    {
        $fromValuesTable = Db::valuesTable($this->db, array_map(static fn($trId) => [
            'site_id' => $siteId,
            'user_id' => $userId,
            'transaction_id' => $trId,
        ], $transactionOrS2pIds), [
            'site_id' => 'int',
            'user_id' => 'bigint',
            'transaction_id' => 'text'
        ], 'tr');

        return (new Query($this->db))
            ->withQuery((new Query($this->db))
                ->from($fromValuesTable), 'tr')
            ->withQuery((new Query($this->db))
                ->select([
                    'name' => 'COALESCE(cps.name, :pay_sys_unmapped)',
                    'tr.transaction_id',
                ])
                ->from(['us' => UserTransactions::TABLE_NAME])
                ->innerJoin('tr', 'us.site_id = tr.site_id AND us.user_id = tr.user_id AND us.transaction_id = tr.transaction_id')
                ->leftJoin(['ps' => PaySystems::TABLE_NAME], 'ps.id = us.pay_sys_id')
                ->leftJoin(['pss' => CanonicalPaySySources::TABLE_NAME], 'pss.name = ps.name AND pss.source = :can_pay_source_product')
                ->leftJoin(['cps' => CanonicalPaySystems::TABLE_NAME], 'cps.id = pss.canonical_pay_sys_id'), 'us')
            ->withQuery((new Query($this->db))
                ->select([
                    'name' => 'COALESCE(cps.name, :pay_sys_unmapped)',
                    'tr.transaction_id',
                ])
                ->from('tr')
                ->innerJoin(['o' => S2pOrders::TABLE_NAME], 'o.site_id = tr.site_id AND o.user_id = tr.user_id AND o.id = tr.transaction_id')
                ->leftJoin('us', 'us.transaction_id = tr.transaction_id')
                ->leftJoin(['ps' => S2pPaySystems::TABLE_NAME], 'ps.id = o.pay_sys_id')
                ->leftJoin(['pss' => CanonicalPaySySources::TABLE_NAME], 'pss.name = ps.code AND pss.source = :can_pay_source_s2p')
                ->leftJoin(['cps' => CanonicalPaySystems::TABLE_NAME], 'cps.id = pss.canonical_pay_sys_id')
                ->where(['us.transaction_id' => null]), 's2p')
            ->from('us')
            ->union((new Query($this->db))->from('s2p'), true)
            ->addParams([
                ':can_pay_source_s2p' => CanonicalPaySySource::SOURCE_S2P,
                ':can_pay_source_product' => CanonicalPaySySource::SOURCE_PRODUCT,
                ':pay_sys_unmapped' => CanonicalPaySystem::CANONICAL_PAY_SYS_UNMAPPED,
            ])
            ->indexBy('transaction_id')
            ->column();
    }

    private static function filterRequisiteIndependentDetailsDuplicates(array $details, bool $requisiteDependentTypesOnly): array
    {
        $typesWithoutRequisite = [];
        $details = array_filter($details, static function ($d) use (&$typesWithoutRequisite) {
            if (in_array($d[UserDocumentProgress::DOC_DETAIL_FIELD_TYPE], KycDetailsRequestModel::DOC_TYPES_REQUISITE_DEPENDENT, true)) {
                return true;
            }
            $typesWithoutRequisite[] = $d[UserDocumentProgress::DOC_DETAIL_FIELD_TYPE];
            return false;
        });

        if ($requisiteDependentTypesOnly) {
            return $details;
        }

        return [...$details, ...array_map(static fn($t) => [UserDocumentProgress::DOC_DETAIL_FIELD_TYPE => $t], array_unique($typesWithoutRequisite))];
    }
}
