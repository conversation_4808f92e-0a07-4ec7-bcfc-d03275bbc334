<?php

declare(strict_types=1);

namespace app\back\components\helpers;

use app\back\components\DateTimeImmutableWithMicroseconds;
use app\back\components\exceptions\InvalidException;
use app\back\components\GlobalParams;
use <PERSON>ron\CronExpression;

class DateHelper
{
    public const string DATE_FORMAT_PHP = 'Y-m-d';
    public const string DATETIME_FORMAT_PHP = 'Y-m-d H:i:s';
    public const string DATETIME_WITHOUT_SECONDS = 'Y-m-d H:i';
    public const string TIME_FORMAT_PHP = 'H:i:s';
    public const string RANGE_PATTERN = '#^(\d{4}-\d{2}-\d{2}(?: \d{2}:\d{2}:\d{2})?)(?:\s?-\s?(\d{4}-\d{2}-\d{2}(?: \d{2}:\d{2}:\d{2})?))?$#';
    public const string RANGE_SEPARATOR = ' - ';
    public const string INTERVAL_PATTERN = '#^(-?)P(?!$)(\d+Y)?(\d+M)?(\d+W)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+S)?)?$#';
    public const int MIDDLE_MONTH_NUMBER = 15;

    public static function getQuarterStartDay(int $time): int
    {
        $q = ceil(date('n', $time) / 3); // current quarter
        $qMonth = $q * 3 - 2; // Number of first month in current quarter
        if ($qMonth < 10) {
            $qMonth = '0' . $qMonth; // Hack but it's the easiest way
        }

        return strtotime(date("Y-$qMonth-01", $time));
    }

    public static function getBirthdays(string $from, ?string $to = null, int $rangeYears = 100): array
    {
        if ($to === null) {
            $to = $from;
        }

        $toYear = date('Y', strtotime($to));
        $fromYear = $toYear - $rangeYears;

        $fromDate = new \DateTimeImmutable($from);
        $toDate = new \DateTimeImmutable($to);

        if ($toDate < $fromDate) {
            throw new InvalidException('From greater than To');
        }

        if ($fromDate->add(\DateInterval::createFromDateString('3 month')) < $toDate) {
            throw new InvalidException('Birthdays for more than 3 months not allowed');
        }

        $interval = \DateInterval::createFromDateString('1 day');
        $currentDate = \DateTime::createFromImmutable($fromDate);
        $result = [];

        do {
            $date = clone $currentDate;
            for ($year = $fromYear; $year <= $toYear; $year++) {
                $date->setDate($year, (int)$date->format('m'), (int)$date->format('d'));
                $result[] = $date->format('Y-m-d');
            }
            $currentDate->add($interval);
        } while ($currentDate <= $toDate);

        // leap years
        if ($fromDate <= $fromDate->setDate((int)$fromDate->format('Y'), 02, 28) && $toDate >= $toDate->setDate((int)$toDate->format('Y'), 03, 01)) {
            for ($year = $fromYear; $year <= $toYear; $year++) {
                $date = new \DateTimeImmutable("$year-01-01");
                if ($date->format('L')) {
                    $result[] = $date->format('Y-02-29');
                }
            }
        }

        return $result;
    }

    /**
     * Convert date from different format and time zones to application time zone
     * @param string|null $val
     * @param string $format
     * @param string $outFormat
     * @return string|null
     */
    public static function parseDate(?string $val, string $format = 'Y-m-d H:i:s', string $outFormat = 'Y-m-d H:i:s'): ?string
    {
        // Invalid datetimes
        if (empty($val) || $val === '1970-01-01 00:00:00' || $val === '0000-00-00 00:00:00') {
            return null;
        }

        $date = \DateTime::createFromFormat($format, $val);

        if (empty($date)) {
            throw new \InvalidArgumentException("Invalid date $val or format $format");
        }

        $date->setTimezone(new \DateTimeZone(GlobalParams::param(GlobalParams::TIMEZONE)));

        return $date->format($outFormat);
    }

    public static function isoInterval(string $fromString, string $toString): string
    {
        $to = new \DateTimeImmutable($toString);
        $from = new \DateTimeImmutable($fromString);

        if ($from->getTimestamp() === $to->getTimestamp()) {
            return 'PT0S';
        }

        return static::intervalToIso($to->diff($from));
    }

    public static function nextDay(string $date): string
    {
        return date('Y-m-d', strtotime('+1 day', strtotime($date)));
    }

    public static function prevDay(string $date): string
    {
        return date('Y-m-d', strtotime('-1 day', strtotime($date)));
    }

    public static function isWeekEnd(int $dateTime): bool
    {
        return date('Y-m-d', $dateTime) === date('Y-m-d', strtotime('sunday this week', $dateTime));
    }

    public static function getStartTimes(string $expression, int $periodMinutes, int $endTime): \Generator
    {
        $cronExpression = new CronExpression($expression);

        $dt = new \DateTime();
        $dt->setTimestamp($endTime - $periodMinutes * 60);
        $step = new \DateInterval('PT1M');
        $end = new \DateTime();
        $end->setTimestamp($endTime);

        while ($dt->add($step) <= $end) {
            if ($cronExpression->isDue($dt)) {
                yield $dt->getTimestamp();
            }
        }
    }

    public static function getPreviousMonthSamePeriod(string $from, string $to): array
    {
        $toTime = strtotime($to);
        $fromTime = strtotime($from);

        if (
            $toTime === false
            || $fromTime === false
            || $toTime < $fromTime
            || date('Ym', $fromTime) !== date('Ym', $toTime)
        ) {
            throw new \InvalidArgumentException('Dates must be in same month and to cannot be greater then from');
        }
        $prevMonthTime = strtotime('first day of previous month', $fromTime);

        $toDay = date('j', $toTime);
        $fromDay = date('j', $fromTime);

        $actualMonthDays = date('t', $fromTime);
        $prevMonthDays = date('t', $prevMonthTime);
        $prevMonthStr = date('Y-m', $prevMonthTime) . '-%02d';

        if ($toDay === $actualMonthDays) {
            $toDay = $prevMonthDays;
        } elseif ($toDay >= $prevMonthDays) {
            $toDay = $prevMonthDays;
        }

        $toDay = min($toDay, $prevMonthDays);
        $fromDay = min($fromDay, $prevMonthDays);

        return ['from' => sprintf($prevMonthStr, $fromDay), 'to' => sprintf($prevMonthStr, $toDay)];
    }

    public static function isQuarterStartDay(?int $timestamp): bool
    {
        return strtotime(date('Y-m-d', $timestamp)) === self::getQuarterStartDay($timestamp);
    }

    public static function isQuarterEndDay(?int $timestamp): bool
    {
        $nextQuarterStartDay = strtotime("+1 day", $timestamp);

        return strtotime(date('Y-m-d', $nextQuarterStartDay)) === self::getQuarterStartDay($nextQuarterStartDay);
    }

    public static function getPreviousQuarterSamePeriod(string $from, string $to, bool $fullPeriod = false): array
    {
        $qFrom = ceil(date('n', strtotime($from)) / 3);
        $qTo = ceil(date('n', strtotime($to)) / 3);
        if ($qFrom !== $qTo) {
            throw new \LogicException('Dates must be in same quarter');
        }

        $daysCount = date_diff(date_create($from), date_create($to))->format('%a');
        $prevFrom = date('Y-m-01', strtotime('-2 month -15 day', strtotime($from)));
        $prevTo = date('Y-m-d', strtotime("+$daysCount day", strtotime($prevFrom)));

        if (strtotime($prevTo) > strtotime($from)) {
            $prevTo = $from;
        }

        if (self::isQuarterEndDay(strtotime($to)) || $fullPeriod) {
            $prevTo = date('Y-m-d', strtotime('-1 day', strtotime($from)));
        }

        return ['from' => $prevFrom, 'to' => $prevTo];
    }

    public static function getPeriodTruncatedDates(string $from, string $to, string $groupPeriod): array
    {
        $from = self::getFirstDayOf($from, $groupPeriod);
        $to = self::getFirstDayOf($to, $groupPeriod);

        if ($from > $to) {
            throw new \InvalidArgumentException('$to must be greater then or equal to $from');
        }

        $resDates = [];
        $currentDate = $from;
        while ($currentDate <= $to) {
            $resDates[] = $currentDate;

            $currentDate = date('Y-m-d', strtotime("next $groupPeriod $currentDate"));
        }

        return $resDates;
    }

    public static function getFirstDayOf(string $date, string $periodType): string
    {
        $time = strtotime($date);

        $from = match ($periodType) {
            'day' => $time,
            'week' => strtotime("this week", $time),
            'month' => strtotime("first day of this month", $time),
        };

        return date('Y-m-d', $from);
    }

    public static function isOverlap(string $xStart, string $xEnd, string $yStart, string $yEnd): bool
    {
        return strtotime($xStart) <= strtotime($yEnd) && strtotime($xEnd) >= strtotime($yStart);
    }

    public static function isTimeInRange(string $time, string $from, string $to): bool
    {
        array_map(static function ($v) {
            if (!preg_match('/^\d{2}:\d{2}:\d{2}$/', $v)) {
                throw new \InvalidArgumentException("Wrong time format, should be `H:i:s`");
            }
        }, compact('time', 'from', 'to'));

        if ($from > $to) {
            return static::isTimeInRange($time, $from, '24:00:00') || static::isTimeInRange($time, '00:00:00', $to);
        }

        return $time >= $from && $time < $to;
    }

    public static function monthBegin(int|string $date = '-1 day'): string
    {
        return date('Y-m-01', self::dateCast($date));
    }

    public static function monthEnd(int|string $date = '-1 day'): string
    {
        return date('Y-m-t', self::dateCast($date));
    }

    public static function today(): string
    {
        return date(self::DATE_FORMAT_PHP);
    }

    public static function yesterday(): string
    {
        return date(self::DATE_FORMAT_PHP, strtotime('-1 day'));
    }

    public static function tomorrow(): string
    {
        return date(self::DATE_FORMAT_PHP, strtotime('+1 day'));
    }

    public static function daysAgo(int $days, string $date = ''): string
    {
        return date(self::DATE_FORMAT_PHP, strtotime("{$date} -{$days} day"));
    }

    public static function weekAgo(?string $now = null): string
    {
        return date(self::DATE_FORMAT_PHP, strtotime('-1 week', $now === null ? time() : strtotime($now)));
    }

    public static function weekAndDayAgo(): string
    {
        return date(self::DATE_FORMAT_PHP, strtotime('- 1 week -1 day'));
    }

    public static function monthAgo(): string
    {
        return date(self::DATE_FORMAT_PHP, strtotime('-1 month'));
    }

    public static function twoMonthsAgo(): string
    {
        return date(self::DATE_FORMAT_PHP, strtotime('-2 month'));
    }

    public static function twoWeeksAgo(): string
    {
        return date(self::DATE_FORMAT_PHP, strtotime('- 2 week'));
    }

    public static function weekBegin(): string
    {
        return date(self::DATE_FORMAT_PHP, strtotime('monday this week'));
    }

    public static function middleMonthPeriod(int|string $baseDate = 'today'): array
    {
        $baseTimestamp = self::dateCast($baseDate);
        if (date('d', $baseTimestamp) < self::MIDDLE_MONTH_NUMBER) {
            $fromDate = date('Y-m-' . self::MIDDLE_MONTH_NUMBER, strtotime('-1 month', $baseTimestamp));
            $toDate = self::monthBegin($baseDate);
        } else {
            $fromDate = self::monthBegin($baseDate);
            $toDate = date('Y-m-' . self::MIDDLE_MONTH_NUMBER, self::dateCast($baseDate));
        }

        return ['from' => $fromDate, 'to' => $toDate];
    }

    public static function splitRange(string $range): array
    {
        if (preg_match(static::RANGE_PATTERN, $range, $matches)) {
            [$from, $to] = [$matches[1], $matches[2] ?? $matches[1]];
        } else {
            throw new \RuntimeException("Invalid dates range format");
        }

        return [$from, $to];
    }

    public static function range(string $from, string $to): string
    {
        return $from . static::RANGE_SEPARATOR . $to;
    }

    // https://stackoverflow.com/a/61088115/8648854
    public static function intervalToIso(\DateInterval $interval, string $default = 'PT0S'): string
    {
        static $f = ['S0F', 'M0S', 'H0M', 'DT0H', 'M0D', 'P0Y', 'Y0M', 'P0M'];
        static $r = ['S', 'M', 'H', 'DT', 'M', 'P', 'Y', 'P'];

        return rtrim(str_replace($f, $r, $interval->format('P%yY%mM%dDT%hH%iM%sS%fF')), 'PT') ?: $default;
    }

    public static function splitPeriod(string $fromStr, string $toStr, string $stepStr = 'P1D', bool $truncateEnd = false): iterable
    {
        $from = new \DateTimeImmutable($fromStr);
        $to = new \DateTimeImmutable($toStr);
        $step = new \DateInterval($stepStr);

        $current = \DateTime::createFromImmutable($from);

        while ($current < $to) {
            $f = clone $current;

            $current->add($step);

            /** @noinspection PhpConditionAlreadyCheckedInspection */
            if ($truncateEnd && $current > $to) {
                $step = $to->diff($f);
                $current = $to;
            }

            yield [$f->format('Y-m-d H:i:s'), $current->format('Y-m-d H:i:s'), self::intervalToIso($step)];
        }
    }

    public static function daysDiff(string $from, string $to): int
    {
        return (int) date_diff(date_create($from), date_create($to))->format('%a');
    }

    public static function sqlTsMultiRangeToArray(?string $source): array
    {
        $result = [];

        if ($source === null || $source === '{}') {
            return [];
        }

        foreach (explode(',', trim($source, '{}')) as $i => $ts) {
            $ts = trim($ts, "[]()\"");
            $groupIndex = (int) ($i / 2);
            if ($i % 2) {
                $ts = self::isoInterval($result[$groupIndex][0], $ts);
            }

            $result[$groupIndex][$i % 2] = $ts;
        }

        return $result;
    }

    private static function dateCast(int|string $date): string|int
    {
        if (is_string($date)) {
            $dateTry = strtotime($date);

            if ($dateTry === false) {
                throw new \InvalidArgumentException("Invalid date: $date");
            }
            $date = $dateTry;
        }

        return $date;
    }

    public static function splitIntervalToTimeUnits(string $startDate, ?string $endDate = null): array
    {
        $startDateObj = new \DateTimeImmutable($startDate);
        $endDateObj = new \DateTimeImmutable($endDate ?? 'now');

        $interval = $endDateObj->diff($startDateObj);
        $totalDays = $interval->days;
        $weeks = (int)($totalDays / 7);
        $remainingDays = $totalDays % 7;
        $hours = $interval->h;

        return [
            'weeks' => $weeks,
            'days' => $remainingDays,
            'hours' => $hours,
            'minutes' => $interval->i,
            'seconds' => $interval->s
        ];
    }

    public static function formatDateRelatively(?string $startDate, ?string $endDate = null, int $displayFirstUnits = 2): ?string
    {
        if (empty($startDate)) {
            return $startDate;
        }

        $result = [];
        foreach (self::splitIntervalToTimeUnits($startDate, $endDate) as $period => $value) {
            if (!empty($value)) {
                $result[] = "$value $period";
            }
        }

        return count($result) ? implode(' ', array_slice($result, 0, $displayFirstUnits)) : 'subsecond';
    }

    public static function now(): string
    {
        return (new \DateTimeImmutable())->format(self::DATETIME_FORMAT_PHP);
    }

    public static function isStringDateTime(?string $string): bool
    {
        if ($string === null) {
            return false;
        }

        try {
            new \DateTimeImmutable($string);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public static function intervalToSeconds(string $interval): int
    {
        $now = new \DateTimeImmutable('1970-01-01 00:00:00'); // date fixed to avoid issues with lapse second and daylight saving time
        $nowWithInterval = $now->add(new \DateInterval($interval));
        return $nowWithInterval->getTimestamp() - $now->getTimestamp();
    }

    public static function fromUnixMilliTimeToDatetimeWithMilliseconds(int $unixMilliTime): DateTimeImmutableWithMicroseconds|false
    {
        return DateTimeImmutableWithMicroseconds::createFromFormat('U.u', number_format($unixMilliTime / 1000, 3, '.', ''));
    }
}
