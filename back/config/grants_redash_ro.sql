SELECT table_name
FROM information_schema.role_table_grants rtg
WHERE rtg.table_schema = 'public' AND grantee = 'redash_ro';

SELECT r.rolname as username, r1.rolname as role
FROM pg_roles r
INNER JOIN pg_auth_members m ON (m.member = r.oid)
INNER JOIN pg_roles r1 ON (m.roleid = r1.oid)
WHERE r.rolcanlogin
ORDER BY 1;

REVOKE SELECT ON ALL TABLES IN SCHEMA public FROM ys_analytic;

GRANT redash_ro to echoslam;
GRANT redash_ro to vlsirko;
GRANT redash_ro to beaulufit;
GRANT redash_ro to handi;
GRANT redash_ro to kakady;
GRANT redash_ro to kashipan;
GRANT redash_ro to kichione;
GRANT redash_ro to luthien;
GRANT redash_ro to magmus;
GRANT redash_ro to nibula;
GRANT redash_ro to plunderer;
GRANT redash_ro to rosgard;
GRANT redash_ro to shurshik;


GRANT SELECT on adjust_events to redash_ro;
GRANT SELECT on adjust_users to redash_ro;
GRANT SELECT on ads_campaigns to redash_ro;
GRANT SELECT on ads_creatives to redash_ro;
GRANT SELECT on ads_stats to redash_ro;
GRANT SELECT on ads_targets to redash_ro;
GRANT SELECT on aff_data to redash_ro;
GRANT SELECT on aff_params to redash_ro;
GRANT SELECT on betting_bets to redash_ro;
GRANT SELECT on betting_events to redash_ro;
GRANT SELECT on betting_log to redash_ro;
GRANT SELECT on betting_odds to redash_ro;
GRANT SELECT on betting_sports to redash_ro;
GRANT SELECT on betting_tournaments to redash_ro;
GRANT SELECT on bins to redash_ro;
GRANT SELECT on bonus to redash_ro;
GRANT SELECT on bonus_activated_user to redash_ro;
GRANT SELECT on bonus_log to redash_ro;
GRANT SELECT on bonus_offers to redash_ro;
GRANT SELECT on bonus_user_progress to redash_ro;
GRANT SELECT on bonus_users_activity to redash_ro;
GRANT SELECT on bonus_users_deposits to redash_ro;
GRANT SELECT on brands to redash_ro;
GRANT SELECT on canonical_pay_sys_sources to redash_ro;
GRANT SELECT on canonical_pay_systems to redash_ro;
GRANT SELECT on chats to redash_ro;
GRANT SELECT on checks to redash_ro;
GRANT SELECT on checks_alerts to redash_ro;
GRANT SELECT on checks_users to redash_ro;
GRANT SELECT on cities to redash_ro;
GRANT SELECT on contacts_service_providers to redash_ro;
GRANT SELECT on contacts_service_providers_rules to redash_ro;
GRANT SELECT on countries to redash_ro;
GRANT SELECT on countries_deps_distr to redash_ro;
GRANT SELECT on crm_campaigns to redash_ro;
GRANT SELECT on crm_campaigns_blocks to redash_ro;
GRANT SELECT on crm_bulks to redash_ro;
GRANT SELECT on crm_letters to redash_ro;
GRANT SELECT on crm_rules to redash_ro;
GRANT SELECT on crm_segments to redash_ro;
GRANT SELECT on employees to redash_ro;
GRANT SELECT on games to redash_ro;
GRANT SELECT on games_sources to redash_ro;
GRANT SELECT on games_vendors to redash_ro;
GRANT SELECT on games_vendors_groups to redash_ro;
GRANT SELECT on hosts to redash_ro;
GRANT SELECT on loyalty_statuses to redash_ro;
GRANT SELECT on lyra_country_geo_sum_stat to redash_ro;
GRANT SELECT on lyra_feature_set_stat to redash_ro;
GRANT SELECT on lyra_feature_sets to redash_ro;
GRANT SELECT on lyra_scenarios to redash_ro;
GRANT SELECT on lyra_scenarios_predictions to redash_ro;
GRANT SELECT on lyra_scenarios_users to redash_ro;
GRANT SELECT on marketing_tids to redash_ro;
GRANT SELECT on pay_gateways to redash_ro;
GRANT SELECT on pay_systems to redash_ro;
GRANT SELECT on rates to redash_ro;
GRANT SELECT on refcodes to redash_ro;
GRANT SELECT on ref_types_rules to redash_ro;
GRANT SELECT on rokeente_sessions to redash_ro;
GRANT SELECT on rokeente_sessions_users to redash_ro;
GRANT SELECT on s2p_antifraud_log to redash_ro;
GRANT SELECT on s2p_orders to redash_ro;
GRANT SELECT on s2p_pay_systems to redash_ro;
GRANT SELECT on s2p_projects to redash_ro;
GRANT SELECT on s2p_statuses to redash_ro;
GRANT SELECT on s2p_transactions to redash_ro;
GRANT SELECT on s2p_users to redash_ro;
GRANT SELECT on sites to redash_ro;
GRANT SELECT on social_nets to redash_ro;
GRANT SELECT on split_tests_logs to redash_ro;
GRANT SELECT on split_tests_users to redash_ro;
GRANT SELECT on stp_applications to redash_ro;
GRANT SELECT on stp_events to redash_ro;
GRANT SELECT on stp_networks to redash_ro;
GRANT SELECT on stp_publishers to redash_ro;
GRANT SELECT on tasks to redash_ro;
GRANT SELECT on tracking to redash_ro;
GRANT SELECT on traffic_sources to redash_ro;
GRANT SELECT on traffic_sources_rules to redash_ro;
GRANT SELECT on useragents to redash_ro;
GRANT SELECT on useragents_apps to redash_ro;
GRANT SELECT on useragents_browsers to redash_ro;
GRANT SELECT on useragents_devices to redash_ro;
GRANT SELECT on useragents_platforms to redash_ro;
GRANT SELECT on users to redash_ro;
GRANT SELECT on users_blocks to redash_ro;
GRANT SELECT on users_clouds_sources to redash_ro;
GRANT SELECT on users_documents to redash_ro;
GRANT SELECT on users_documents_faces to redash_ro;
GRANT SELECT on users_documents_faces_similarity to redash_ro;
GRANT SELECT on users_documents_logs to redash_ro;
GRANT SELECT on users_documents_progress to redash_ro;
GRANT SELECT on users_documents_text_manual_labels to redash_ro;
GRANT SELECT on users_documents_text_predictions to redash_ro;
GRANT SELECT on users_documents_text_predictions_models to redash_ro;
GRANT SELECT on users_events to redash_ro;
GRANT SELECT on users_events_checkout to redash_ro;
GRANT SELECT on users_games_tokens to redash_ro;
GRANT SELECT on users_history to redash_ro;
GRANT SELECT on users_ignores_ids to redash_ro;
GRANT SELECT on users_kyc to redash_ro;
GRANT SELECT on users_limits to redash_ro;
GRANT SELECT on users_logins to redash_ro;
GRANT SELECT on users_loyalty to redash_ro;
GRANT SELECT on users_lyra_activity to redash_ro;
GRANT SELECT on users_lyra_geo to redash_ro;
GRANT SELECT on users_max_win to redash_ro;
GRANT SELECT on users_metrics to redash_ro;
GRANT SELECT on users_multi_accounts to redash_ro;
GRANT SELECT on users_remarketing to redash_ro;
GRANT SELECT on users_special_info to redash_ro;
GRANT SELECT on tournaments to redash_ro;
GRANT SELECT on users_tickets to redash_ro;
GRANT SELECT on users_tickets_files to redash_ro;
GRANT SELECT on users_tickets_log to redash_ro;
GRANT SELECT on users_tournaments to redash_ro;
GRANT SELECT on users_transactions to redash_ro;
GRANT SELECT on users_transactions_info to redash_ro;
GRANT SELECT on users_wallets to redash_ro;
GRANT SELECT on users_wallets_snapshots to redash_ro;
GRANT SELECT on visits to redash_ro;
GRANT SELECT on withdrawals to redash_ro;
GRANT SELECT on withdrawals_comments to redash_ro;
GRANT SELECT on withdrawals_prediction to redash_ro;
GRANT SELECT on withdrawals_rules to redash_ro;
GRANT SELECT on wp_aff_owner_groups to redash_ro;
GRANT SELECT on wp_cpa to redash_ro;
GRANT SELECT on wp_programs to redash_ro;
GRANT SELECT on wp_webmasters to redash_ro;
GRANT SELECT on wp_webmasters_programs to redash_ro;
GRANT SELECT on yh_calls to redash_ro;
