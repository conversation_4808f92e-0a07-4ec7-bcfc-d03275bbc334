<?php

declare(strict_types=1);

use app\back\modules\task\actions\maintenance\UsersStatusesMigrateTask;
use app\back\modules\task\actions\sync\BonusOffersProcessTask;
use app\back\modules\task\actions\update\{
    AdjustUsersTask,
    checks\UsersChecksLyraTask,
    checks\UsersLoginsChecksLyraTask,
    checks\UsersLoginsRokeenteSessionsChecksLyraTask,
    checks\UsersLyraActivityCheckTask,
    checks\UsersStatsChecksLyraTask,
    CrmLettersOpensTask,
    HostsInfoFromHostsTask,
    LyraUsersScenariosTask,
    RokeenteSessionsLyraActivityTask,
    RokeenteSessionsUsersTask,
    SitesEmailsDomainsTask,
    SplitTestsUsersCleanTask,
    UsersBalancesDailyTask,
    UsersCleanTask,
    UsersContactsFromUsersTask,
    UsersContactsPriorityTask,
    UsersDevicesDeleteDuplicateNullTask,
    UsersDevicesTask,
    UsersFavoriteCrmChannelTask,
    UsersFromLoginsTask,
    UsersGamesMetricsTask,
    UsersGamesRawCleanTask,
    UsersGamesRecommendsInitTask,
    UsersGamesRecommendsInferTask,
    UsersGamesRecommendsTrainTask,
    UsersIgnoresFromLoginsTask,
    UsersIgnoresFromUsersTask,
    UsersLastActivityTask,
    UsersLoginsLyraGeoTask,
    UsersLoginsRegTask,
    UsersLyraScenariosCleanTask,
    UsersMetricBonusExcessiveTask,
    UsersMetricChargebackProbabilityTask,
    UsersMetricsBetsFavoritesTask,
    UsersMetricsBetsFirstAtTask,
    UsersMetricsExpiredCleanTask,
    UsersMetricVipProbabilityTask,
    UsersRokeenteSessionsLyraGeoTask,
    UsersSpecialInfoFromLoginsTask,
    UsersTransactionsDepsNumberTask,
    UsersTransactionsFirstsTask,
    UsersTransactionsGameGroupTask,
    UsersTransactionsLoginTask,
    UsersLyraGeoFromTransactionsTask,
    UsersTransactionsMetricsFullTask,
    UsersTransactionsMetricsIncrementalTask,
    UsersStatusesDailyTask,
    UsersStatusesByRulesTask,
    UsersStatusesNormalTask,
    UsersStatusesPaidTask,
    UsersStatusesToPreVipTask,
    UsersWalletsSnapshots,
    WithdrawalsAcceptTask,
    WithdrawalsAcceptUaTask,
    WithdrawalsAutoProcessTask,
    WithdrawalsRejectTask};

return [
    /* Update */
    'update-adjust-users' => AdjustUsersTask::class,
    'update-checks-users-logins-lyra' => UsersLoginsChecksLyraTask::class,
    'update-checks-users-logins-rokeente-sessions-lyra' => UsersLoginsRokeenteSessionsChecksLyraTask::class,
    'update-checks-users-lyra' => UsersChecksLyraTask::class,
    'update-checks-users-lyra-activity' => UsersLyraActivityCheckTask::class,
    'update-checks-users-stats-lyra' => UsersStatsChecksLyraTask::class,
    'update-crm-letters-opens' => ['class' => CrmLettersOpensTask::class, 'openPeriod' => '3 month'],
    'update-hosts-info-from-hosts' => HostsInfoFromHostsTask::class,
    'update-lyra-users-scenarios' => LyraUsersScenariosTask::class,
    'update-lyra-users-scenarios-clean' => UsersLyraScenariosCleanTask::class,
    'update-rokeente-sessions-users' => RokeenteSessionsUsersTask::class,
    'update-sites-emails-domains' => SitesEmailsDomainsTask::class,
    'update-split-tests-users-clean' => ['class' => SplitTestsUsersCleanTask::class, 'storeInterval' => 'P1M'],
    'update-users-balances-daily' => UsersBalancesDailyTask::class,
    'update-users-clean' => ['class' => UsersCleanTask::class, 'storeInterval' => '6 month'],
    'update-users-contacts-from-users' => UsersContactsFromUsersTask::class,
    'update-users-contacts-priority' => UsersContactsPriorityTask::class,
    'update-users-devices' => UsersDevicesTask::class,
    'update-users-devices-delete-duplicate-null' => UsersDevicesDeleteDuplicateNullTask::class,
    'update-users-favorite-crm-channel' => UsersFavoriteCrmChannelTask::class,
    'update-users-from-logins' => UsersFromLoginsTask::class,
    'update-users-games-metrics' => UsersGamesMetricsTask::class,
    'update-users-games-raw-clean' => ['class' => UsersGamesRawCleanTask::class, 'storeInterval' => '1 month'],
    'update-users-games-recommends-infer' => UsersGamesRecommendsInferTask::class,
    'update-users-games-recommends-init' => UsersGamesRecommendsInitTask::class,
    'update-users-games-recommends-train' => UsersGamesRecommendsTrainTask::class,
    'update-users-ignores-from-logins' => UsersIgnoresFromLoginsTask::class,
    'update-users-ignores-from-users' => UsersIgnoresFromUsersTask::class,
    'update-users-last-activity' => UsersLastActivityTask::class,
    'update-users-logins-lyra-geo' => UsersLoginsLyraGeoTask::class,
    'update-users-logins-reg' => UsersLoginsRegTask::class,
    'update-users-metric-bonus-excessive' => UsersMetricBonusExcessiveTask::class,
    'update-users-metric-chargeback-probability' => UsersMetricChargebackProbabilityTask::class,
    'update-users-metric-vip-probability' => UsersMetricVipProbabilityTask::class,
    'update-users-metrics-bets-first-at' => UsersMetricsBetsFirstAtTask::class,
    'update-users-metrics-bets-favorites' => UsersMetricsBetsFavoritesTask::class,
    'update-users-metrics-expired-clean' => UsersMetricsExpiredCleanTask::class,
    'update-users-rokeente-sessions-lyra-activity' => RokeenteSessionsLyraActivityTask::class,
    'update-users-rokeente-sessions-lyra-geo' => UsersRokeenteSessionsLyraGeoTask::class,
    'update-users-special-info-from-logins' => UsersSpecialInfoFromLoginsTask::class,
    'update-users-stats-dep-number' => UsersTransactionsDepsNumberTask::class, // TODO: remove after fist deploy
    'update-users-transactions-dep-number' => UsersTransactionsDepsNumberTask::class,
    'update-users-stats-firsts' => UsersTransactionsFirstsTask::class, // TODO: remove after fist deploy
    'update-users-transactions-firsts' => UsersTransactionsFirstsTask::class,
    'update-users-transactions-game-group' => UsersTransactionsGameGroupTask::class,
    'update-users-stats-login' => UsersTransactionsLoginTask::class, // TODO: remove after fist deploy
    'update-users-transactions-login' => UsersTransactionsLoginTask::class,
    'update-users-lyra-geo-from-transactions' => UsersLyraGeoFromTransactionsTask::class,
    'update-users-stats-metrics-full' => UsersTransactionsMetricsFullTask::class, // TODO: remove after fist deploy
    'update-users-transactions-metrics-full' => UsersTransactionsMetricsFullTask::class,
    'update-users-stats-metrics-incremental' => UsersTransactionsMetricsIncrementalTask::class, // TODO: remove after fist deploy
    'update-users-transactions-metrics-incremental' => UsersTransactionsMetricsIncrementalTask::class,
    'update-users-statuses-by-rules-daily' => ['class' => UsersStatusesByRulesTask::class, 'checkPeriod' => \app\back\entities\UserStatusVipThreshold::CHECK_PERIOD_DAY],
    'update-users-statuses-by-rules-weekly' => ['class' => UsersStatusesByRulesTask::class, 'checkPeriod' => \app\back\entities\UserStatusVipThreshold::CHECK_PERIOD_WEEK],
    'update-users-statuses-by-rules-monthly' => ['class' => UsersStatusesByRulesTask::class, 'checkPeriod' => \app\back\entities\UserStatusVipThreshold::CHECK_PERIOD_MONTH],
    'update-users-statuses-paid' => UsersStatusesPaidTask::class,
    'update-users-statuses-daily' => UsersStatusesDailyTask::class,
    'update-users-statuses-migrate' => UsersStatusesMigrateTask::class,
    'update-users-statuses-normal-daily' => UsersStatusesNormalTask::class,
    'update-users-statuses-normal-monthly' => ['class' => UsersStatusesNormalTask::class, 'shiftFrom' => '- 1 month', 'shiftTo' => '-1 day'],
    'update-users-statuses-to-pre-vip' => UsersStatusesToPreVipTask::class,
    'update-users-wallets-snapshots' => UsersWalletsSnapshots::class,
    'update-withdrawals-accept' => WithdrawalsAcceptTask::class,
    'update-withdrawals-accept-ua' => WithdrawalsAcceptUaTask::class,
    'update-withdrawals-auto-process' => WithdrawalsAutoProcessTask::class,
    'update-withdrawals-reject' => WithdrawalsRejectTask::class,

    /* Syncs */
    'sync-bonus-offers-process' => BonusOffersProcessTask::class,
];
