<?php

declare(strict_types=1);

use app\back\config\tasks\Res;
use app\back\modules\task\requests\S2pChunkedJsonRequest;
use app\back\modules\task\requests\S2pDigestRequest;

$s2p = [
    's2p-bins' => [
        'class' => \app\back\modules\task\actions\import\s2p\S2pBinsTask::class,
        'request' => [
            'class' => S2pChunkedJsonRequest::class,
            'host' => 'https://bins.start2pay.com',
            'url' => 'bins/all',
            'aliases' => [
                'bin' => 'bin',
                'system' => 'cardBrand',
                'bank' => 'issuerBankName',
                'type' => 'cardType',
                'status' => 'cardLevel',
                'country' => 'country',
            ],
        ],
    ],
    's2p-orders' => [
        'class' => \app\back\modules\task\actions\import\s2p\S2pOrdersTask::class,
        'request' => [
            'class' => S2pDigestRequest::class,
            'url' => 'private/order/1.0/list',
            'curlTimeout' => 600,
            'aliases' => [
                'id' => 'uuid',
                'context' => 'payment_context',
                'invoice_id' => 'invoice',
                'status' => 'status',
                'pay_type_name' => 'type',
                'type' => 'payment_direction',
                'payment_system' => 'payment_system',
                'payment_system_type_name' => 'pay_system_type',
                'status_detail' => 'status_detail',
                'requisites' => 'requisites',
                'pay_method' => 'payment_method',
                'pay_class' => 'payment_class',
                'device' => 'display_options.device',
                'device_type' => 'custom.device_info.type',
                'payer_identity' => 'payer_identity',

                'refcode' => 'ref_code',
                'useragent' => 'display_options.p_user_agent',

                'client_original_amount' => 'client_original_amount',
                'client_original_currency' => 'client_original_currency',

                'ps_original_amount' => 'pay_system_original_amount',
                'ps_original_currency' => 'pay_system_original_currency',

                'project' => 'project',
                'date_created' => 'created',
                'date' => 'modified',
                'success_at' => 'payed',
                'process_at' => 'process_created',
                'mid_name' => 'mid_name',
                'initiated_by' => 'custom.initiated_by',
                'approved_by' => 'custom.approved_by',
                'comment_arr' => 'order_comment_general',
                'comment_ps_arr' => 'order_comment_pay_system',
                'routing_branch' => 'x_branch',

                'mid_amount' => 'mid_principal_amount',
                'mid_currency' => 'mid_principal_currency_code',

                'host' => 'display_options.success_url',
            ],
        ],
    ],
    's2p-transactions' => [
        'class' => \app\back\modules\task\actions\import\s2p\S2pTransactionsTask::class,
        'request' => [
            'class' => S2pDigestRequest::class,
            'url' => 'private/transaction/1.0/list',
            'aliases' => [
                'id' => 'uuid',
                'type' => 'type',
                'parent_id' => 'parent_uuid',
                'status' => 'status',
                'dir' => 'pay_direction',
                'order_id' => 'order_uuid',
                'status_detail' => 'status_detail',
                'project_name' => 'project_name',
                'pay_class_code' => 'pay_class_code',
                'pay_system_code' => 'pay_system_code',
                'created_at' => 'created',
                'updated_at' => 'modified',
                'success_at' => 'success_date',

                'amount' => 'pay_system_original_amount',
                'currency' => 'pay_system_original_code',
                'amount_usd' => 'pay_system_original_amount_usd',
                'rate' => 'pay_system_original_ratio',

                'tr_fee_amount' => 'transaction_fee_amount',
                'tr_fee_currency' => 'transaction_fee_code',
                'tr_fee_amount_usd' => 'transaction_fee_amount_usd',
                'tr_fee_rate' => 'transaction_fee_ratio',

                'mid_fee_amount' => 'mid_fee_amount',
                'mid_fee_currency' => 'mid_fee_code',
                'mid_fee_amount_usd' => 'mid_fee_amount_usd',
                'mid_fee_rate' => 'mid_fee_ratio',
            ],
        ],
    ],
    's2p-antifraud-log' => [
        'class' => \app\back\modules\task\actions\import\s2p\S2pAntifraudLogTask::class,
        'request' => [
            'class' => \app\back\modules\task\requests\S2pListRequest::class,
            'url' => 'private/antifraud/1.0/requisite-list-history/:listId?limit=:limit',
            'aliases' => [
                'id' => 'id',
                'requisite_type' => 'requisite_type',
                'requisite' => 'requisite',
                'action' => 'action',
                'list_name' => 'list_name',
                'created_at' => 'created',
            ],
        ],
    ],
    's2p-users-pay-systems' => [
        'class' => \app\back\modules\task\actions\import\s2p\S2pUsersPaySystemsTask::class,
        'request' => [
            'class' => S2pDigestRequest::class,
            'url' => 'private/deny_log/list',
            'aliases' => [
                'project' => 'project_name',
                'user_id' => 'project_user_value',
                'pay_system' => 'pay_system_code',
                'allowed' => 'actual_state',
                'created_at' => 'created',
            ],
        ],
    ],
    'update-bins' => \app\back\modules\task\actions\update\BinsTask::class,
    'update-priority-pay-sys' => \app\back\modules\task\actions\update\PriorityPaySysTask::class,
    'update-s2p-orders-firsts' => \app\back\modules\task\actions\update\S2pOrdersFirstsTask::class,
    'update-s2p-users-rank' => \app\back\modules\task\actions\update\S2pUsersRankTask::class,
    'update-users-requisites-from-s2p-orders' => \app\back\modules\task\actions\update\UsersRequisitesFromS2pOrdersTask::class,
    'update-s2p-projects-flag' => \app\back\modules\task\actions\update\S2pProjectsFlagTask::class,
];

$s2pEvents = [
    'users-events-checkout-s2p' => [
        'class' => \app\back\modules\task\actions\import\s2p\UsersEventsCheckoutS2pTask::class,
        'request' => [
            'class' => \app\back\modules\task\requests\S2pEventRequest::class,
            'url' => 'api/v1/corpan/events',
            'aliases' => [
                'event_type' => 'event_type',
                'event_id' => 'event_id',
                'context_id' => 'context_id',
                'client_created_at' => 'occurred_at',
                'server_received_at' => 'received_at',
                'event_payload' => 'event_payload',
            ],
            'path' => 'items',
        ],
    ],
];

// Tasks configs per resource (site)
return [
    Res::PLATFORM_S2P => $s2p,
    Res::S2P => [
        's2p-orders' => ['instanceId' => \app\back\entities\S2pOrder::INSTANCE_S2P],
        's2p-transactions' => ['instanceId' => \app\back\entities\S2pOrder::INSTANCE_S2P],
    ],
    Res::S2P_IDEA => [
        's2p-orders' => ['instanceId' => \app\back\entities\S2pOrder::INSTANCE_IDEA],
        's2p-transactions' => ['instanceId' => \app\back\entities\S2pOrder::INSTANCE_IDEA],
    ],
    Res::S2P_W4P => [
        's2p-orders' => ['instanceId' => \app\back\entities\S2pOrder::INSTANCE_W4P],
        's2p-transactions' => ['instanceId' => \app\back\entities\S2pOrder::INSTANCE_W4P],
    ],

    Res::PLATFORM_S2P_EVENTS => $s2pEvents,
    Res::S2P_EVENTS_AWS => ['users-events-checkout-s2p' => ['instanceId' => \app\back\entities\UserEventCheckout::INSTANCE_S2P]],
    Res::S2P_EVENTS_AWS_IDEA => ['users-events-checkout-s2p' => ['instanceId' => \app\back\entities\UserEventCheckout::INSTANCE_IDEA]],
    Res::S2P_EVENTS_AWS_W4P => ['users-events-checkout-s2p' => ['instanceId' => \app\back\entities\UserEventCheckout::INSTANCE_W4P]],
    Res::S2P_EVENTS_GCP => ['users-events-checkout-s2p' => ['instanceId' => \app\back\entities\UserEventCheckout::INSTANCE_S2P]],
    Res::S2P_EVENTS_GCP_IDEA => ['users-events-checkout-s2p' => ['instanceId' => \app\back\entities\UserEventCheckout::INSTANCE_IDEA]],
    Res::S2P_EVENTS_GCP_W4P => ['users-events-checkout-s2p' => ['instanceId' => \app\back\entities\UserEventCheckout::INSTANCE_W4P]],
];
