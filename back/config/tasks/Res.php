<?php

declare(strict_types=1);

namespace app\back\config\tasks;

use app\back\entities\Site;

class Res
{
    /* Platforms */
    public const string PLATFORM_SMEN = 'P_SMEN';
    public const string PLATFORM_GI = 'P_GI';
    public const string PLATFORM_GI_WITH_BETTING = 'P_GI_WITH_BETTING';
    public const string PLATFORM_YS = 'P_YS';
    public const string PLATFORM_YS_RG = 'P_YS_RG'; // RedninesGaming
    public const string PLATFORM_YS_SOFTSWISS = 'P_YS_SS';
    public const string PLATFORM_BETTING = 'P_BETTING';
    public const string PLATFORM_WITH_BETTING = 'P_WITH_BETTING';
    public const string PLATFORM_HHS = 'P_HHS';
    public const string PLATFORM_STP = 'P_STP';
    public const string PLATFORM_GGATE = 'P_GGATE';
    public const string PLATFORM_S2P = 'P_S2P';
    public const string PLATFORM_S2P_EVENTS = 'P_S2P_EVENTS';

    public const array PLATFORM_RESOURCES_SMEN = [
        ...self::PLATFORM_RESOURCES_SMEN_WITH_BETTING,
        self::GMS, self::GMSD, self::RUBIN, self::ARM, self::PHB,
        self::VIP, self::EL,self::V24, self::JC, self::ONX, self::RC,
        self::MS, self::VS, self::LOS, self::VTR,
        self::K7, self::VOX,
        ...(APP_ENV_DEV ? [self::SMEN_SPACE_09] : [])
    ];
    public const array PLATFORM_RESOURCES_GI = [
        ...self::PLATFORM_RESOURCES_GI_WITH_BETTING,
        self::ICG, self::CSBET, self::HOT, self::SLTR,
    ];
    public const array PLATFORM_RESOURCES_GI_WITH_BETTING = [
        self::VV, self::GGB, self::VBET, self::DDBET, self::FSC, self::GGUA, self::VERDE, self::BTR, self::VSP, self::HIT, self::NVC, self::FC,
    ];
    public const array PLATFORM_RESOURCES_SMEN_WITH_BETTING = [
        self::CV, self::V777, self::S7, self::AWI, self::VP, self::SZL, self::MSL, self::WIN, self::K7,
    ];
    public const array PLATFORM_RESOURCES_WITH_BETTING = [
        ...self::PLATFORM_RESOURCES_GI_WITH_BETTING,
        ...self::PLATFORM_RESOURCES_SMEN_WITH_BETTING,
        self::MRB, // YS
    ];
    public const array PLATFORM_RESOURCES_YS = [
        self::MRB, self::SPC, self::BB, self::KRV, self::XON,
    ];
    public const array PLATFORM_RESOURCES_YS_SOFTSWISS = [
        self::SND,
    ];
    public const array PLATFORM_RESOURCES_YS_RG = [
        self::GGBUK,
    ];
    public const array PLATFORM_RESOURCES_BETTING = [
        self::CV_BETTING, self::V777_BETTING, self::S7_BETTING, self::AWI_BETTING, self::VP_BETTING, self::SZL_BETTING, self::MSL_BETTING, self::WIN_BETTING, self::K7_BETTING, // SMEN
        self::VV_BETTING, self::GGB_BETTING, self::VBET_BETTING, self::DDBET_BETTING, self::FSC_BETTING, self::GGUA_BETTING, self::VERDE_BETTING, self::BTR_BETTING, self::VSP_BETTING, self::HIT_BETTING, self::NVC_BETTING, self::FC_BETTING,// GI
        self::MRB_BETTING, // YS
    ];
    public const array PLATFORM_RESOURCES_HHS = [
        self::HHS_EE, self::HHS_WHITE, self::HHS_SMEN, self::HHS_FLAME,
    ];
    public const array PLATFORM_RESOURCES_STP = [
        self::STP, self::STP_OLD,
    ];
    public const array PLATFORM_RESOURCES_GGATE = [
        self::GGD1, self::GGHD, self::GGP1,
    ];
    public const array PLATFORM_RESOURCES_S2P = [
        self::S2P, self::S2P_IDEA, self::S2P_W4P,
    ];
    public const array PLATFORM_RESOURCES_EVENTS_S2P = [
        self::S2P_EVENTS_AWS, self::S2P_EVENTS_AWS_W4P, self::S2P_EVENTS_AWS_IDEA, self::S2P_EVENTS_GCP, self::S2P_EVENTS_GCP_W4P, self::S2P_EVENTS_GCP_IDEA,
    ];

    public const array PLATFORMS_RESOURCES = [
        self::PLATFORM_SMEN => self::PLATFORM_RESOURCES_SMEN,
        self::PLATFORM_GI => self::PLATFORM_RESOURCES_GI,
        self::PLATFORM_GI_WITH_BETTING => self::PLATFORM_RESOURCES_GI_WITH_BETTING,
        self::PLATFORM_YS => self::PLATFORM_RESOURCES_YS,
        self::PLATFORM_YS_SOFTSWISS => self::PLATFORM_RESOURCES_YS_SOFTSWISS,
        self::PLATFORM_YS_RG => self::PLATFORM_RESOURCES_YS_RG,
        self::PLATFORM_BETTING => self::PLATFORM_RESOURCES_BETTING,
        self::PLATFORM_WITH_BETTING => self::PLATFORM_RESOURCES_WITH_BETTING,
        self::PLATFORM_HHS => self::PLATFORM_RESOURCES_HHS,
        self::PLATFORM_STP => self::PLATFORM_RESOURCES_STP,
        self::PLATFORM_GGATE => self::PLATFORM_RESOURCES_GGATE,
        self::PLATFORM_S2P => self::PLATFORM_RESOURCES_S2P,
        self::PLATFORM_S2P_EVENTS => self::PLATFORM_RESOURCES_EVENTS_S2P,
    ];

    public const string DEFAULT = 'DEFAULT';

    /* SMEN */
    public const string GMS = 'GMS';
    public const string IC = 'IC';
    public const string CV = 'CV';
    public const string GMSD = 'GMSD';
    public const string RUBIN = 'RUBIN';
    public const string ARM = 'ARM';
    public const string PHB = 'PHB';
    public const string VIP = 'VIP';
    public const string EL = 'EL';
    public const string V24 = 'V24';
    public const string JC = 'JC';
    public const string ONX = 'ONX';
    public const string RC = 'RC';
    public const string J188 = 'J188';
    public const string V777 = 'V777';
    public const string MS = 'MS';
    public const string SZL = 'SZL';
    public const string VS = 'VS';
    public const string S7 = 'S7';
    public const string LOS = 'LOS';
    public const string VTR = 'VTR';
    public const string AWI = 'AWI';
    public const string K7 = 'K7';
    public const string VP = 'VP';
    public const string MSL = 'MSL';
    public const string KRV = 'KRV';
    public const string VOX = 'VOX';
    public const string WIN = 'WIN';

    public const string SMEN_SPACE_09 = 'SMEN_SPACE_09'; // SMEN dev server for analytics

    public const string CV_BETTING = 'CV_BETTING';
    public const string V777_BETTING = 'V777_BETTING';
    public const string S7_BETTING = 'S7_BETTING';
    public const string AWI_BETTING = 'AWI_BETTING';
    public const string MRB_BETTING = 'MRB_BETTING';
    public const string VP_BETTING = 'VP_BETTING';
    public const string SZL_BETTING = 'SZL_BETTING';
    public const string MSL_BETTING = 'MSL_BETTING';
    public const string WIN_BETTING = 'WIN_BETTING';
    public const string K7_BETTING = 'K7_BETTING';

    /* GI */
    public const string VBC = 'VBC';
    public const string VV = 'VV';
    public const string GGB = 'GGB';
    public const string VCH = 'VCH';
    public const string ICG = 'ICG';
    public const string VERDE = 'VERDE';
    public const string CSBET = 'CSBET';
    public const string VBET = 'VBET';
    public const string DDBET = 'DDBET';
    public const string FSC = 'FSC';
    public const string HOT = 'HOT';
    public const string GGUA = 'GGUA';
    public const string HIT = 'HIT';
    public const string SLTR = 'SLTR';
    public const string BTR = 'BTR';
    public const string VSP = 'VSP';
    public const string NVC = 'NVC';
    public const string FC = 'FC';

    public const string VV_BETTING = 'VV_BETTING';
    public const string GGB_BETTING = 'GGB_BETTING';
    public const string VBET_BETTING = 'VBET_BETTING';
    public const string DDBET_BETTING = 'DDBET_BETTING';
    public const string FSC_BETTING = 'FSC_BETTING';
    public const string GGUA_BETTING = 'GGUA_BETTING';
    public const string VERDE_BETTING = 'VERDE_BETTING';
    public const string BTR_BETTING = 'BTR_BETTING';
    public const string VSP_BETTING = 'VSP_BETTING';
    public const string HIT_BETTING = 'HIT_BETTING';
    public const string NVC_BETTING = 'NVC_BETTING';
    public const string FC_BETTING = 'FC_BETTING';

    /* Yellow stone */
    public const string MRB = 'MRB';
    public const string GGBUK = 'GGBUK';
    public const string SPC = 'SPC';
    public const string SND = 'SND';
    public const string BB = 'BB';
    public const string XON = 'XON';

    /* Golden Gate */
    public const string GGD1 = 'GGD1';
    public const string GGHD = 'GGHD';
    public const string GGP1 = 'GGP1';

    /* Not products */
    public const string S2P = 'S2P';
    public const string S2P_IDEA = 'S2P_IDEA';
    public const string S2P_W4P = 'S2P_W4P';

    public const string S2P_EVENTS_AWS = 'S2P_EVENTS_AWS';
    public const string S2P_EVENTS_AWS_IDEA = 'S2P_EVENTS_AWS_IDEA';
    public const string S2P_EVENTS_AWS_W4P = 'S2P_EVENTS_AWS_W4P';

    public const string S2P_EVENTS_GCP = 'S2P_EVENTS_GCP';
    public const string S2P_EVENTS_GCP_IDEA = 'S2P_EVENTS_GCP_IDEA';
    public const string S2P_EVENTS_GCP_W4P = 'S2P_EVENTS_GCP_W4P';

    public const string HHS_SMEN = 'HHS_SMEN';
    public const string HHS_FLAME = 'HHS_FLAME';
    public const string HHS_EE = 'HHS_EE';
    public const string HHS_WHITE = 'HHS_WHITE';
    public const string BUF = 'BUF';
    public const string CHECK = 'CHECK';
    public const string CHECK_CRITICAL = 'CHECK_CRITICAL';
    public const string YHLP = 'YHLP';
    public const string CRM = 'CRM';
    public const string SEND = 'SEND';
    public const string OXR = 'OXR';
    public const string WEBMONEY = 'WEBMONEY';
    public const string L4P = 'L4P';
    public const string P4E = 'P4E';
    public const string MR = 'MR';
    public const string MRT = 'MRT';
    public const string WP = 'WP';
    public const string BETTING = 'BETTING';
    public const string STP = 'STP';
    public const string STP_OLD = 'STP_OLD';
    public const string CID = 'CID';
    public const string SYNEF_CLOUD = 'SYNEF_CLOUD';
    public const string ADS_UNITY = 'ADS_UNITY';
    public const string ADS_IRON = 'ADS_IRON';
    public const string ADS_VUNGLE = 'ADS_VUNGLE';
    public const string ADS_GOOGLE = 'ADS_GOOGLE';
    public const string ADS_FACEBOOK = 'ADS_FACEBOOK';
    public const string ADS_MINTEGRAL = 'ADS_MINTEGRAL';
    public const string VIPAFF = 'VIPAFF';
    public const string VIPAFF_CP = 'VIPAFF_CP';
    public const string FACES = 'FACES';
    public const string ROK = 'ROK';
    public const string YS_ML_SEG = 'YS_ML_SEG';
    public const string COIN_API = 'COIN_API';
    public const string GIN_ML = 'GIN_ML';
    public const string STP_ADJUST = 'STP_ADJUST';
    public const string ML_HUB = 'ML_HUB';
    public const string JIRA = 'JIRA';

    /**
     * Used in both directions
     * First resource for site must be main resource
     */
    public const array RESOURCE_TO_SITE = [
        self::ARM => Site::ARM,
        self::AWI => Site::AWI,
        self::AWI_BETTING => Site::AWI,
        self::BB => Site::BB,
        self::BTR => Site::BTR,
        self::BTR_BETTING => Site::BTR,
        self::CSBET => Site::CSBET,
        self::CV => Site::CV,
        self::CV_BETTING => Site::CV,
        self::DDBET => Site::DDBET,
        self::DDBET_BETTING => Site::DDBET,
        self::EL => Site::EL,
        self::FC => Site::FC,
        self::FC_BETTING => Site::FC,
        self::FSC => Site::FSC,
        self::FSC_BETTING => Site::FSC,
        self::GGB => Site::GGB,
        self::GGBUK => Site::GGBUK,
        self::GGB_BETTING => Site::GGB,
        self::GGD1 => Site::GGD1,
        self::GGHD => Site::GGHD,
        self::GGP1 => Site::GGP1,
        self::GGUA => Site::GGUA,
        self::GGUA_BETTING => Site::GGUA,
        self::GMS => Site::GMS,
        self::GMSD => Site::GMSD,
        self::HIT => Site::HIT,
        self::HIT_BETTING => Site::HIT,
        self::HOT => Site::HOT,
        self::ICG => Site::ICG,
        self::JC => Site::JC,
        self::K7 => Site::K7,
        self::K7_BETTING => Site::K7,
        self::KRV => Site::KRV,
        self::LOS => Site::LOS,
        self::MRB => Site::MRB,
        self::MRB_BETTING => Site::MRB,
        self::MS => Site::MS,
        self::MSL => Site::MSL,
        self::MSL_BETTING => Site::MSL,
        self::NVC => Site::NVC,
        self::NVC_BETTING => Site::NVC,
        self::ONX => Site::ONX,
        self::PHB => Site::PHB,
        self::RC => Site::RC,
        self::RUBIN => Site::RUBIN,
        self::S7 => Site::S7,
        self::S7_BETTING => Site::S7,
        self::SLTR => Site::SLTR,
        self::SMEN_SPACE_09 => Site::SMEN_SPACE_09,
        self::SND => Site::SND,
        self::SPC => Site::SPC,
        self::SZL => Site::SZL,
        self::SZL_BETTING => Site::SZL,
        self::V24 => Site::V24,
        self::V777 => Site::V777,
        self::V777_BETTING => Site::V777,
        self::VBET => Site::VBET,
        self::VBET_BETTING => Site::VBET,
        self::VERDE => Site::VERDE,
        self::VERDE_BETTING => Site::VERDE,
        self::VIP => Site::VIP,
        self::VOX => Site::VOX,
        self::VP => Site::VP,
        self::VP_BETTING => Site::VP,
        self::VS => Site::VS,
        self::VSP => Site::VSP,
        self::VSP_BETTING => Site::VSP,
        self::VTR => Site::VTR,
        self::VV => Site::VV,
        self::VV_BETTING => Site::VV,
        self::WIN => Site::WIN,
        self::WIN_BETTING => Site::WIN,
        self::XON => Site::XON,
    ];

    public static function all(): array
    {
        $rc = new \ReflectionClass(static::class);

        $constants = $rc->getConstants(\ReflectionClassConstant::IS_PUBLIC);
        $result = array_filter($constants, static fn (string $name) => !(str_starts_with($name, 'PLATFORM') || $name === 'RESOURCE_TO_SITE'), ARRAY_FILTER_USE_KEY);
        sort($result);

        return array_values($result);
    }

    public static function getSiteIdByResourceName(string $name): int
    {
        $siteId = self::RESOURCE_TO_SITE[$name] ?? null;
        if ($siteId === null) {
            throw new \InvalidArgumentException("Cannot find site for resource $name");
        }

        return $siteId;
    }

    public static function getResourceNameBySiteId(int $siteId): string
    {
        foreach (self::RESOURCE_TO_SITE as $res => $site) {
            if ($site === $siteId) {
                return $res;
            }
        }

        throw new \InvalidArgumentException("Cannot find resource for site $siteId");
    }

    public static function getPlatformByResource(string $resource): ?string
    {
        foreach (self::PLATFORMS_RESOURCES as $platform => $resources) {
            if (in_array($resource, $resources, true)) {
                return $platform;
            }
        }

        return null;
    }

    public static function isResourceExists(string $resource): bool
    {
        return defined("self::$resource") && is_string(self::{$resource}) && (str_starts_with($resource, 'P_')) === false;
    }
}
