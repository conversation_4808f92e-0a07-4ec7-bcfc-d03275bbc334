<?php

declare(strict_types=1);

use app\back\config\tasks\Res;

$bettingCredentials = static fn(int|string|null $wlId = null) => [
    'host' => 'http://betting-ad-integration.stage-betting.ginsp.net', // Self signed ssl error
    'authKey' => 'd3d2f8fe44bea4f7aa70eb1dcc35a2da',
    'cryptEnabled' => false,
    'wlId' => $wlId,
];

$giCredentials = static fn(int $brandId) => [
    'host' => 'https://ad-integration.platform-s00.ginsp.net',
    'authKey' => '94f6d70188dbcec7fce5d2e079372306',
    'brandId' => $brandId,
];

return [
    // SMEN
    Res::GMS => [
        'host' => 'https://gmsl.stagings.tools',
        'authKey' => 'api_password',
    ],
    Res::CV => [
        'host' => 'https://cv.stagings.tools',
        'authKey' => 'api_password',
    ],
    Res::SMEN_SPACE_09 => [
        'host' => 'https://cv-09.smen.space',
        'authKey' => 'api_password',
    ],
    Res::GMSD => [
        'host' => 'https://gmsd.stagings.tools',
        'authKey' => 'api_password',
    ],
    Res::RUBIN => [
        'host' => 'https://cvo.stagings.tools',
        'authKey' => 'api_password',
    ],
    Res::ARM => [
        'host' => 'https://adm.stagings.tools',
        'authKey' => 'api_password',
    ],
    Res::PHB => [
        'host' => 'https://ph.stagings.tools',
        'authKey' => 'api_password',
    ],
    Res::VIP => [
        'host' => 'https://vip.stagings.tools',
        'authKey' => 'api_password',
    ],
    Res::EL => [
        'host' => 'https://el.stagings.tools',
        'authKey' => 'wwerwerwlmncjeiwoeowero933344',
    ],
    Res::V24 => [
        'host' => 'https://v24.stagings.tools',
        'authKey' => 'ddfsjlsdkfjskfjslfjsdkf',
    ],
    Res::JC => [
        'host' => 'https://joy.stagings.tools',
        'authKey' => 'werisdfshdksdfsfsd',
    ],
    Res::ONX => [
        'host' => 'https://vd.stagings.tools',
        'authKey' => 'api_password',
    ],
    Res::RC => [
        'host' => 'https://vr.stagings.tools',
        'authKey' => 'adfdsssword',
    ],
    Res::V777 => [
        'host' => 'https://vua.stagings.tools',
        'authKey' => 'adfdsssword',
    ],
    Res::MS => [
        'host' => 'https://mbs.stagings.tools',
        'authKey' => 'adfdsssword',
    ],
    Res::SZL => [
        'host' => 'https://szl.stagings.tools',
        'authKey' => 'adfdsssword',
    ],
    Res::VS => [
        'host' => 'https://vs.stagings.tools',
        'authKey' => 'adfdsssword',
    ],
    Res::S7 => [
        'host' => 'https://7sl.stagings.tools',
        'authKey' => 'ddfqweqweqweqwfjskfjslfjsdkf',
    ],
    Res::LOS => [
        'host' => 'https://los.stagings.tools',
        'authKey' => '5befdef1f25d55f40cf4c20bf1adf9e8',
    ],
    Res::VTR => [
        'host' => 'https://vtr.stagings.tools',
        'authKey' => 'ddfqweqweqweqwfjskfjslfjsdkf',
    ],
    Res::AWI => [
        'host' => 'https://awi.stagings.tools',
        'authKey' => 'ddfqweqweqweqwfjskfjslfjsdkf',
    ],
    Res::K7 => [
        'host' => 'https://7k.stagings.tools',
        'authKey' => 'ddfqweqweqweqwfjskfjslfjsdkf',
    ],
    Res::VP => [
        'host' => 'https://vp.stagings.tools',
        'authKey' => 'ddfqweqweqweqwfjskfjslfjsdkf',
    ],
    Res::MSL => [
        'host' => 'https://msl.stagings.tools',
        'authKey' => 'ddfqweqweqweqwfjskfjslfjsdkf',
    ],
    Res::VOX => [
        'host' => 'https://vox.stagings.tools',
        'authKey' => '5befdef1f25d55f40cf4c20bf1adf9e8',
    ],
    Res::WIN => [
        'host' => 'https://win.stagings.tools',
        'authKey' => 'ddfqweqweqweqwfjskfjslfjsdkf',
    ],

    // GI
    'GGB' => $giCredentials(4),
    'VV' => $giCredentials(5),
    'ICG' => $giCredentials(7),
    'VERDE' => $giCredentials(8),
    'CSBET' => $giCredentials(11),
    'FSC' => $giCredentials(16),
    'VBET' => $giCredentials(19),
    'DDBET' => $giCredentials(20),
    'GGUA' => $giCredentials(22),
    'HIT' => $giCredentials(34),
    'HOT' => $giCredentials(35),
    'SLTR' => $giCredentials(51),
    'BTR' => $giCredentials(52),
    'VSP' => $giCredentials(53),
    'NVC' => $giCredentials(58),
    'FC' => $giCredentials(59),

    // The SMEN sites with GIN betting
    'CV_BETTING' => $bettingCredentials('a100'),
    'V777_BETTING' => $bettingCredentials('vulkan777'),
    'S7_BETTING' => $bettingCredentials('7slots'),
    'AWI_BETTING' => $bettingCredentials('awi'),
    'VP_BETTING' => $bettingCredentials('vplatinum'),
    'SZL_BETTING' => $bettingCredentials('szl'),
    'MSL_BETTING' => $bettingCredentials('masalbet'),
    'WIN_BETTING' => $bettingCredentials('winnita'),
    'K7_BETTING' => $bettingCredentials('7k'),

    // GIN betting available sites
    'GGB_BETTING' => $bettingCredentials(4),
    'VV_BETTING' => $bettingCredentials(5),
    'VERDE_BETTING' => $bettingCredentials(8),
    'FSC_BETTING' => $bettingCredentials(16),
    'VBET_BETTING' => $bettingCredentials(19),
    'DDBET_BETTING' => $bettingCredentials(20),
    'GGUA_BETTING' => $bettingCredentials(22),
    'HIT_BETTING' => $bettingCredentials(34),
    'BTR_BETTING' => $bettingCredentials(52),
    'VSP_BETTING' => $bettingCredentials(53),
    'NVC_BETTING' => $bettingCredentials('nvcasino'),
    'FC_BETTING' => $bettingCredentials('fortunica'),

    // YS betting available sites
    'MRB_BETTING' => $bettingCredentials('mrbet'),

    Res::CRM => [
        'host' => 'https://fscrm-dev.hwtool.biz',
        'client' => 'analytics',
        'authKey' => 'YJfQ36F8zobmkt2x3XR9W6PzgzmbpZEmLfBFeUfd',
    ],
    Res::OXR => [
        'host' => 'https://openexchangerates.org',
        'appId' => '********************************',
        'clientLogin' => '<EMAIL>',
        'clientPassword' => 'PxPSgc9jgXHfB',
    ],
    Res::COIN_API => [
        'host' => 'https://rest.coinapi.io',
        // <NAME_EMAIL>
        'authKey' => 'EC90C9C4-9134-482A-ADF0-14B0E9BBCCF8',
    ],
    Res::YHLP => [
        'host' => 'https://rest.dev.yhelper.net',
        'authKey' => '8f4804b3c5bd0e4c30df9ff5a7d89296',
        'callAuthKey' => 'b3c572e40ca18ca1a7b0e95831f5287a',
    ],
    Res::BUF => [
        'host' => 'https://dev-buffer.anal.tools',
        'authKey' => 'd01bcb233062ebe2d0745d92794e0a22',
    ],
    Res::BETTING => $bettingCredentials(),
    Res::S2P => [
        'authKey' => 'statistic:HLp8_T-_x4ZYfqLexUj5',
        'host' => 'https://sandstatistic.start2pay.com',
    ],
    Res::S2P_EVENTS_AWS => [
        'authKey' => 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6InMycC1zYW5kYm94LWF3cy0yMDI1MDUwMSJ9.eyJzdWIiOiJjb3JwYW4iLCJpYXQiOjE3NDc3MzQ3OTB9.3ivnsZO1giXZPHEpiGg0GEMkrLRd9rNZNX0NMFp82xw',
        'host' => 'https://aws-sandbox-gotrack-feed.start2pay.link',
    ],
    Res::SYNEF_CLOUD => [
        'host' => 'https://keys.syneforge.com',
    ],
    Res::VIPAFF => [
        'host' => 'https://dev-platform-01.vipaff.com',
        'authKey' => '3YRre2Uw3ruYolR-UyxYCsdf3Wfg55iveekcv[qzx7',
    ],
    Res::STP_ADJUST => [
        'host' => 'https://s2s.adjust.com',
    ],
    Res::ML_HUB => [
        'host' => $_ENV['ML_HUB_HOST'] ?? '',
        'authKey' => $_ENV['ML_HUB_AUTH_KEY'] ?? '',
    ],
    Res::FACES => [
        'host' => $_ENV['FACE_NET_URL'] ?? '',
        'authKey' => ($_ENV['FACE_NET_USER'] ?? '') . ':' . ($_ENV['FACE_NET_PASS'] ?? ''),
    ],
];
