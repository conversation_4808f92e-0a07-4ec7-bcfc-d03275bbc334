<?php

declare(strict_types=1);

namespace app\config\schedule;

use app\back\config\tasks\Res;

$devResources = array_merge(
    [Res::GMS, Res::CV, Res::RUBIN, Res::GMSD, Res::ARM, Res::PHB, Res::VIP, Res::V24, Res::JC, Res::ONX, Res::RC, Res::EL, Res::V777, Res::MS, Res::SZL, Res::VS, Res::S7, Res::LOS, Res::SMEN_SPACE_09, Res::VTR, Res::AWI, Res::K7, Res::VP, Res::MSL, Res::VOX, Res::WIN], // PLATFORM_SMEN
    [Res::VBET, Res::VV, Res::GGB, Res::ICG, Res::VERDE, Res::HOT, Res::GGUA, Res::HIT, Res::SLTR, Res::BTR, Res::VSP, Res::FC], // PLATFORM_GI
    [Res::GGB_BETTING, Res::VBET_BETTING, Res::VERDE_BETTING, Res::BTR_BETTING, Res::VSP_BETTING, Res::HIT_BETTING, Res::FC_BETTING], // PLATFORM_BETTING,
    [Res::BUF, Res::CHECK, Res::COIN_API, Res::VIPAFF, Res::BETTING, Res::YHLP, Res::CRM, Res::OXR, Res::S2P], // Non sites
    [Res::DEFAULT, Res::CID],
);

$devTasks = array_flip([
    'betting-bets',
    'betting-events',
    'betting-log',
    'betting-tournaments',
    'bonus',
    'bonus-activated-user',
    'bonus-black-list',
    'bonus-event-option',
    'bonus-journal',
    'bonus-offers',
    'bonus-user-progress',
    'bonus-user-progress-free-spins',
    'bonus-users-promo-codes',
    'buf-adjust',
    'buf-common',
    'buf-visits',
    'crm-bulks',
    'crm-campaigns',
    'crm-campaigns-blocks',
    'crm-letters',
    'crm-letters-clicks',
    'crm-letters-opens',
    'crm-rules',
    'games-sources',
    'ignore-users',
    'login-tokens-gin',
    'login-tokens-smen',
    'lootboxes',
    'lootbox-prizes',
    'lootbox-user-prize-log',
    'loyalty-statuses',
    'phone-confirmation-requests',
    'rates-crypto',
    'rates-latest',
    'referral-blacklist',
    'referral-references',
    'referral-referrers',
    's2p-orders',
    's2p-users-pay-systems',
    'split-tests',
    'split-tests-logs',
    'split-tests-logs-gi',
    'split-tests-users',
    'sync-bonus-offers-process',
    //'sync-secret-mirrors',
    'sync-withdrawals',
    'update-adjust-users',
    'update-betting-log-from-logins',
    'update-betting-odds-parts',
    'update-checks-all-1-min',
    'update-checks-all-10-min',
    'update-checks-betting-bets-lyra',
    'update-checks-sites-1-min',
    'update-checks-sites-10-min',
    'update-checks-users-logins-lyra',
    'update-checks-users-lyra-activity',
    'update-metrics-from-bonus-user-progress',
    'update-refcodes-source-site',
    'update-refcodes-ts',
    'update-refcodes-webmaster',
    'update-sites-emails-domains',
    'update-split-tests-logs-users',
    'update-users-balances-daily',
    'update-users-cids',
    'update-users-contacts-from-users',
    'update-users-contacts-from-users-requisites',
    'update-users-contacts-priority',
    'update-users-devices',
    'update-users-favorite-crm-channel',
    'update-users-from-logins',
    'update-users-games-aggregate',
    'update-users-games-from-betting-log',
    'update-users-games-metrics',
    'update-users-ignores-from-users',
    'update-users-last-activity',
    'update-users-logins-reg',
    'update-users-max-win',
    'update-users-metric-bonus-excessive',
    'update-users-metrics-bets-favorites',
    'update-users-requisites-from-s2p-orders',
    'update-users-requisites-from-users-transactions',
    'update-users-similar-faces-recursive',
    'update-users-special-info-from-logins',
    'update-users-transactions-dep-number',
    'update-users-transactions-firsts',
    'update-users-transactions-login',
    'update-users-transactions-metrics-full',
    'update-users-transactions-metrics-incremental',
    'update-users-statuses-paid',
    'update-users-statuses-daily',
    'update-withdrawals-auto-process',
    'update-withdrawals-delay',
    'update-withdrawals-planned',
    'users',
    'users-betting-profit-segments',
    'users-devices',
    'users-devices-guests',
    'users-devices-mobile',
    'users-documents',
    'users-events',
    'users-games-bets',
    'users-games-chosen',
    'users-games-raw',
    'users-games-tokens',
    'users-limits',
    'users-logins',
    'users-loyalty',
    'users-remarketing',
    'users-self-exclude',
    'users-special-info',
    'users-stats',
    'users-stats-bonuses',
    'users-stats-fun',
    'users-stats-win-back-ys',
    'users-unsubscribe',
    'users-wallets-bonus',
    'users-wallets-real',
    'vipaff-comments',
    'vipaff-refcodes',
    'vipaff-users',
    'yhlp-calls',
    'yhlp-chats',
    'yhlp-chats-messages',
    'yhlp-chats-topics',
    'yhlp-events',
    'yhlp-mails',
    'yhlp-operators',
    'yhlp-topics',
    'yhlp-users-process-history',
    'yhlp-users-time-zone',
]);

$tasksSchedule = require __DIR__ . '/../prod/tasks_schedule.php';

foreach ($tasksSchedule['schedule'] as $period => $tasks) {
    foreach ($tasks as $task => $resources) {
        if (!isset($devTasks[$task])) {
            unset($tasksSchedule['schedule'][$period][$task]);
            continue;
        }

        $tasksSchedule['schedule'][$period][$task] = [];
        foreach ($resources as $resourceOrIndex => $queueOrResource) {
            $resource = is_numeric($resourceOrIndex) ? $queueOrResource : $resourceOrIndex;
            if (in_array($resource, $devResources, true)) {
                if (is_numeric($resourceOrIndex)) {
                    $tasksSchedule['schedule'][$period][$task][] = $resource;
                } else {
                    $tasksSchedule['schedule'][$period][$task][$resource] = $queueOrResource;
                }
            }
        }

        if (count($tasksSchedule['schedule'][$period][$task]) === 0) {
            unset($tasksSchedule['schedule'][$period][$task]);
        }
    }
    if (count($tasksSchedule['schedule'][$period]) === 0) {
        unset($tasksSchedule['schedule'][$period]);
    }
}

foreach ($tasksSchedule['dependencies'] as $i => $dependency) {
    $tasksSchedule['dependencies'][$i][1] = array_intersect((array)$dependency[1], $devResources);
    foreach ($dependency as $i2 => $dep) {
        if ($i2 === 0 || $i2 === 1) {
            continue; // Skip parent task and resource
        }

        if (is_array($dep)) { // $dep = array of all resources, we need to leave only available
            $tasksSchedule['dependencies'][$i][$i2] = array_intersect($dep, $devResources);
        }
    }
}

return $tasksSchedule;
