<?php

declare(strict_types=1);

namespace app\config\schedule;

use app\back\config\tasks\Res;

/* Platforms */
const P_SMEN = Res::PLATFORM_RESOURCES_SMEN;
const P_SMEN_WITH_BETTING = Res::PLATFORM_RESOURCES_SMEN_WITH_BETTING;
const P_GI = Res::PLATFORM_RESOURCES_GI;
const P_GI_WITH_BETTING = Res::PLATFORM_RESOURCES_GI_WITH_BETTING;
const P_YS = Res::PLATFORM_RESOURCES_YS;
const P_YS_RG = Res::PLATFORM_RESOURCES_YS_RG;
const P_YS_SS = Res::PLATFORM_RESOURCES_YS_SOFTSWISS;
const P_BETTING = Res::PLATFORM_RESOURCES_BETTING;
const P_WITH_BETTING = Res::PLATFORM_RESOURCES_WITH_BETTING;
const P_GGATE = Res::PLATFORM_RESOURCES_GGATE;
const P_S2P = Res::PLATFORM_RESOURCES_S2P;
const P_S2P_EVENTS = Res::PLATFORM_RESOURCES_EVENTS_S2P;

/* Queues */
const Q_SMEN_WD = 'SMEN_WD';
const Q_GI_WD = 'GI_WD';
const Q_GI_GAMES = 'GI_GAMES';
const Q_VV_GAMES = 'VV_GAMES';
const Q_SLOW = 'SLOW';

const P_CIS = [...P_SMEN, ...P_GI];
const P_YS_MAIN = [...P_YS, ...P_YS_RG];
const P_MAIN = [...P_CIS, ...P_YS_MAIN];
const P_ALL = [...P_MAIN, ...P_YS_SS, ...P_GGATE];
$giGamesQueuesConfig = array_merge(array_fill_keys(array_diff(P_GI, [Res::VV]), Q_GI_GAMES), [Res::VV => Q_VV_GAMES]);
$wdQueuesConfig = array_merge(
    array_fill_keys(P_SMEN, Q_SMEN_WD),
    array_fill_keys(P_GI, Q_GI_WD),
);

/* Periods */
const PERIOD_1_MINUTE = 1;
const PERIOD_5_MINUTES = 2;
const PERIOD_10_MINUTES = 3;
const PERIOD_10_MINUTES_OVERLAP = 4;
const PERIOD_1_HOUR = 5;
const PERIOD_1_HOUR_END = 6;
const PERIOD_4_HOURS = 7;
const PERIOD_PREV_DAY_HALF_HOUR = 8;
const PERIOD_PREV_DAY_1_HOUR = 9;
const PERIOD_PREV_DAY_3_HOURS = 10;
const PERIOD_PREV_DAY_4_HOURS = 11;
const PERIOD_PREV_DAY_7_HOURS = 12;
const PERIOD_CUR_DAY_18_HOUR = 13;
const PERIOD_MON_WED_FRI = 14;
const PERIOD_MONDAY = 15;
const PERIOD_1_8_15_22_DOM = 16;
const PERIOD_MONTH_BEGIN = 17;
const PERIOD_FRIDAY = 18;
const PERIOD_8_HOURS = 19;
const PERIOD_10_MINUTES_SHIFTED = 20;
const PERIOD_MORN_TUE_TILL_SAT = 21;
const PERIOD_MORN_SUN_MON = 22;
const PERIOD_CUR_DAY_7_HOURS = 23;
const PERIOD_CUR_DAY_10_HOURS_EEST = 24;
const PERIOD_1_MINUTE_SHIFTED_10 = 25;
const PERIOD_HALF_MONTH = 26;
const PERIOD_TUE_TILL_SAT_9_HOURS_EEST = 27;
const PERIOD_SUN_MON_9_HOURS_EEST = 28;
const PERIOD_CUR_DAY_8_HOURS_EEST = 29;
const PERIOD_5_MINUTES_SHIFTED_2 = 30;
const PERIOD_PREV_2_WEEKS_7_HOURS = 31;
const PERIOD_PREV_DAY_MIDNIGHT = 32;
const PERIOD_MONTH_MIDNIGHT = 33;
const PERIOD_1_MINUTE_SHIFTED_120  = 34;

// Tasks schedule
return [
    'cronPeriods' => [                          //m h dom mon dow               from shift          period              priority    EEST
        PERIOD_1_MINUTE =>                      ['* * * * *',                   '-70 seconds',      'PT80S',            1,          0],
        PERIOD_1_MINUTE_SHIFTED_120 =>          ['* * * * *',                   '-120 seconds',     'PT1M',             2,          0],
        PERIOD_1_MINUTE_SHIFTED_10 =>           ['* * * * *',                   '-10 minutes',      'PT1M',             2,          0],
        PERIOD_5_MINUTES =>                     ['*/5 * * * *',                 '-5 minutes -10 seconds', 'PT5M10S',    5,          0],
        PERIOD_5_MINUTES_SHIFTED_2 =>           ['*/5 * * * *',                 '-7 minutes',       'PT5M',             5,          0],
        PERIOD_10_MINUTES =>                    ['2,12,22,32,42,52 * * * *',    '-12 minutes',      'PT10M',            10,         0],
        PERIOD_10_MINUTES_OVERLAP =>            ['*/10 * * * *',                '-10 minutes',      'PT11M',            10,         0],
        PERIOD_10_MINUTES_SHIFTED =>            ['*/10 * * * *',                '-20 minutes',      'PT10M',            15,         0],
        PERIOD_1_HOUR =>                        ['1 * * * *',                   '-61 minutes',      'PT1H',             20,         0],
        PERIOD_1_HOUR_END =>                    ['58 * * * *',                  '-58 minutes',      'PT1H',             20,         0],
        PERIOD_4_HOURS =>                       ['0 */4 * * *',                 '-4 hours',         'PT4H',             100,        0],
        PERIOD_8_HOURS =>                       ['0 */8 * * *',                 '-8 hours',         'PT8H',             100,        0],
        PERIOD_PREV_DAY_MIDNIGHT =>             ['0 0 * * *',                   'yesterday',        'P1D',              50,         0],
        PERIOD_PREV_DAY_HALF_HOUR =>            ['30 0 * * *',                  'yesterday',        'P1D',              50,         0],
        PERIOD_PREV_DAY_1_HOUR =>               ['0 1 * * *',                   'yesterday',        'P1D',              50,         0],
        PERIOD_PREV_DAY_3_HOURS =>              ['0 3 * * *',                   'yesterday',        'P1D',              50,         0],
        PERIOD_PREV_DAY_4_HOURS =>              ['0 4 * * *',                   'yesterday',        'P1D',              50,         0],
        PERIOD_PREV_DAY_7_HOURS =>              ['0 7 * * *',                   'yesterday',        'P1D',              50,         0],
        PERIOD_CUR_DAY_7_HOURS =>               ['0 7 * * *',                   'today',            'P1D',              50,         0],
        PERIOD_CUR_DAY_8_HOURS_EEST =>          ['0 8 * * *',                   'today',            'P1D',              50,         1],
        PERIOD_CUR_DAY_10_HOURS_EEST =>         ['0 10 * * *',                  'today',            'P1D',              50,         1],
        PERIOD_CUR_DAY_18_HOUR =>               ['5 18 * * *',                  'today',            'P1D',              50,         0],
        PERIOD_TUE_TILL_SAT_9_HOURS_EEST =>     ['0 9 * * 2,3,4,5,6',           'today',            '',                 50,         1],
        PERIOD_SUN_MON_9_HOURS_EEST =>          ['0 9 * * 1,7',                 'today',            '',                 50,         1],
        PERIOD_MONDAY =>                        ['0 3 * * 1',                   'yesterday',        '',                 50,         0],
        PERIOD_FRIDAY =>                        ['0 3 * * 5',                   'yesterday',        '',                 50,         0],
        PERIOD_MON_WED_FRI =>                   ['0 3 * * 1,3,5',               'yesterday',        '',                 50,         0],
        PERIOD_MORN_TUE_TILL_SAT =>             ['0 8 * * 2,3,4,5,6',           'today',            '',                 50,         0],
        PERIOD_MORN_SUN_MON =>                  ['0 8 * * 1,7',                 'today',            '',                 50,         0],
        PERIOD_1_8_15_22_DOM =>                 ['0 3 1,8,15,22 * *',           'today',            '',                 50,         0],
        PERIOD_MONTH_MIDNIGHT =>                ['0 0 1 * *',                   'today',            '',                 50,         0],
        PERIOD_MONTH_BEGIN =>                   ['0 3 1 * *',                   'today',            '',                 50,         0],
        PERIOD_HALF_MONTH =>                    ['0 4 1,15 * *',                'today',            '',                 50,         0],
        PERIOD_PREV_2_WEEKS_7_HOURS =>          ['0 7 * * *',                   '-15 days',        'P14D',              50,         0],
    ],
    'schedule' => [
        // task frequency table
        PERIOD_1_MINUTE => [
            'users'                             => P_ALL,
            'users-stats'                       => P_ALL,
            'users-stats-bonuses'               => P_CIS,
            'users-wallets-bonus'               => P_MAIN,
            'users-wallets-betting'             => P_SMEN_WITH_BETTING,
            'users-wallets-real'                => P_MAIN,
            'update-withdrawals-planned'        => $wdQueuesConfig,
            'sync-withdrawals'                  => $wdQueuesConfig,
            's2p-orders'                        => P_S2P,
            'update-users-contacts-priority'    => [...P_SMEN, ...P_YS_MAIN],
            'hhs-users-games-sessions'          => [Res::HHS_SMEN, Res::HHS_FLAME, Res::HHS_EE, Res::HHS_WHITE],
            'sync-bonus-offers-process'         => P_CIS,
            'bonus-offers'                      => P_SMEN,
            'l4p-users'                         => [Res::P4E, Res::L4P],
            'users-kyc-status'                  => P_GI,
            'update-checks-users-expired'       => [Res::CHECK],
            'update-checks-sites-1-min'         => [Res::CHECK],
            'update-checks-all-1-min'           => [Res::CHECK],
            'update-checks-tasks-queues-1-min'  => [Res::CHECK_CRITICAL],
            'update-checks-users-lyra'          => P_MAIN,
            'update-checks-users-stats-lyra'    => P_MAIN,
            'update-users-ignores-from-users'   => P_MAIN,
            'update-users-contacts-from-users'  => P_MAIN,
            'users-events-checkout'             => P_SMEN,
        ],
        PERIOD_1_MINUTE_SHIFTED_120  => [
            'users-events-checkout-s2p'         => P_S2P_EVENTS,
        ],
        PERIOD_1_MINUTE_SHIFTED_10 => [
            'bonus-journal'                     => P_GI,
        ],
        PERIOD_5_MINUTES => [
            'login-tokens-gin'                  => P_GI,
            'login-tokens-smen'                 => P_SMEN,
            'bonus-user-progress'               => P_SMEN,
            'bonus-user-progress-free-spins'    => P_SMEN,
            'users-logins'                      => P_MAIN,
            'update-rokeente-sessions-users'    => P_MAIN,
            'ignore-users'                      => P_SMEN,
            'users-stats-fun'                   => P_SMEN,

            'update-users-transactions-firsts'                => P_ALL,
            'update-users-transactions-metrics-incremental'   => P_ALL,
            'update-users-logins-reg'           => P_MAIN,
            'update-users-from-logins'          => P_MAIN,
            'update-users-special-info-from-logins' => P_MAIN,
            'update-users-requisites-from-s2p-orders' => [Res::S2P],
            'update-users-requisites-from-users-transactions' => P_GI,
            'update-users-contacts-from-users-requisites' => P_GI,
            'update-users-contacts-priority'    => P_GI,

            'users-games-tokens'                => P_SMEN,
            'users-games-bets'                  => [...P_SMEN_WITH_BETTING, ...P_YS_MAIN],
            'users-games-raw'                   => [...P_YS_MAIN, ...P_YS_SS],
            'update-users-games-aggregate'      => [...P_YS_MAIN, ...P_YS_SS],

            'betting-log'                       => P_GI_WITH_BETTING,
            'update-betting-log-from-logins'    => P_GI_WITH_BETTING,
            'update-users-games-from-betting-log' => P_GI_WITH_BETTING,
            'betting-tournaments'               => [Res::BETTING],
            'betting-events'                    => P_BETTING,
            'betting-bets'                      => P_BETTING,
            'update-betting-odds-parts'         => P_BETTING,
            'update-checks-betting-bets-lyra'   => P_BETTING,

            'update-users-last-activity'        => P_SMEN,
            'update-users-metrics-bets-first-at' => P_SMEN,

            'update-s2p-orders-firsts'          => [Res::S2P],
            'update-withdrawals-delay'          => [Res::ARM],

            'users-tickets'                     => [Res::JIRA],
            'users-tickets-files'               => [Res::JIRA],
        ],
        PERIOD_5_MINUTES_SHIFTED_2 => [
            'users-games-raw'                   => $giGamesQueuesConfig,
            'update-users-games-aggregate'      => $giGamesQueuesConfig,
            'update-users-last-activity'        => $giGamesQueuesConfig,
            'update-users-metrics-bets-first-at' => $giGamesQueuesConfig,
        ],
        PERIOD_10_MINUTES => [
            'users-devices'                     => P_CIS,
            'users-devices-mobile'              => P_GI,
            'users-devices-guests'              => P_GI,
            //'users-devices-safari'              => PLATFORM_GI,
            'tournaments-users'                 => P_CIS,
            'sync-secret-mirrors'               => P_SMEN,
            'update-users-max-win'              => [...P_MAIN, ...P_YS_SS],
            'games-sources'                     => P_SMEN,
            'games-sources-updated'             => P_YS_MAIN,
            'users-unsubscribe'                 => P_CIS,
            'users-self-exclude'                => P_CIS,
            'users-kyc-status'                  => P_GI, // Remove after 2024-09-26
            'bonus-activated-user'              => P_SMEN,
            'bonus-users-activity'              => P_SMEN,
            'bonus-black-list'                  => P_SMEN,
            'bonus-black-list-gin'              => P_GI,
            'bonus-users-promo-codes'           => P_SMEN,
            'users-stats-admin-comments'        => P_GI,
            'users-blocks'                      => array_diff(P_MAIN, [Res::VIP, ...P_YS_RG]),
            'bonus-user-deposit'                => P_SMEN,
            'users-multi-accounts'              => P_SMEN,
            'users-stats-contexts'              => P_YS_MAIN,
            'users-documents'                   => array_diff(P_MAIN, [Res::GGBUK]),
            'users-documents-sum-sub'           => P_YS,
            'update-documents-tags-recognize'   => [Res::GIN_ML],
            'phone-confirmation-requests'       => P_SMEN,
            'visits-log-ys'                     => P_YS_MAIN,
            'users-comments'                    => P_GI,
            'users-special-info'                => P_CIS,
            'referral-referrers'                => P_SMEN,
            'referral-references'               => P_SMEN,
            'referral-blacklist'                => P_SMEN,
            'users-limits'                      => P_GI,
            'users-stats-win-back-ys'           => P_YS,
            'users-checkbox-receipts'           => [RES::GGUA],
            'users-stats-refunds'               => [RES::GGUA],

            'update-users-transactions-login'          => P_MAIN,
            'update-users-stats-details-from-login' => P_GI,
            //'update-users-statuses-to-pre-vip'  => P_MAIN,
            'update-users-statuses-paid'             => P_MAIN,
            //'update-users-devices'              => ALL_SITES, // TODO: remove
            'update-users-devices-delete-duplicate-null' => P_MAIN,
            'update-affiliates-codes-stats-from-visits-log' => P_YS_MAIN,
            'update-refcodes-ts'                => [Res::DEFAULT],
            'update-refcodes-ref-type'          => [Res::DEFAULT],
            'update-checks-sites-10-min'        => [Res::CHECK],
            'update-checks-all-10-min'          => [Res::CHECK],
            'update-checks-tasks-queues-10-min' => [Res::CHECK_CRITICAL],
            'update-withdrawals-reject'         => P_SMEN,
            //'update-withdrawals-accept'         => array_diff(PLATFORM_SMEN, [VS, LOS, SZL]),
            'update-withdrawals-auto-process'   => P_CIS,
            //'update-withdrawals-accept-ua'      => [VCUA],
            'update-checks-users-logins-lyra'   => P_MAIN,
            'update-checks-users-lyra-activity' => P_MAIN,

            'yhlp-users-process-history'        => [Res::YHLP],
            'yhlp-chats'                        => [Res::YHLP],
            'yhlp-chats-messages'               => [Res::YHLP],
            'yhlp-topics'                       => [Res::YHLP],
            'yhlp-chats-topics'                 => [Res::YHLP],
            'yhlp-mails'                        => [Res::YHLP],
            'yhlp-calls'                        => [Res::YHLP],

            'crm-rules'                        => [Res::CRM],
            'crm-letters'                      => [Res::CRM],
            'crm-letters-opens'                => [Res::CRM],
            'crm-letters-clicks'               => [Res::CRM],
            'crm-campaigns'                    => [Res::CRM],
            'crm-campaigns-blocks'             => [Res::CRM],
            'crm-bulks'                        => [Res::CRM],
            'crm-split-variants'               => [Res::CRM],

            's2p-transactions'                  => P_S2P,
            's2p-antifraud-log'                 => P_S2P,
            's2p-users-pay-systems'             => P_S2P,

            'split-tests'                       => P_SMEN,
            'split-tests-logs'                  => P_SMEN,
            'split-tests-users'                 => P_SMEN,
            'update-split-tests-logs-users'     => P_SMEN,
            'split-tests-logs-gi'               => P_GI,

            'stp-events'                        => [Res::STP, Res::STP_OLD],
            'stp-customers-data'                => [Res::STP_OLD],

            'lootboxes'                         => array_diff(P_CIS, [Res::GMS, Res::VTR]),
            'lootbox-prizes'                    => array_diff(P_SMEN, [Res::GMS, Res::VTR]),
            'lootbox-user-prize-log'            => array_diff(P_CIS, [Res::GMS, Res::VTR]),

            'send-notify-new-phone'             => [Res::SEND],
            'send-notify-vip-dep-fail'          => [Res::SEND],
            'send-notify-vip-wd-fail'           => [Res::SEND],
            'send-notify-wd-new'                => [Res::SEND],
            'send-notify-ultra-first-login'     => [Res::SEND],
            'send-notify-vip-awol-login'        => [Res::SEND],
            'send-notify-big-win-back'          => [Res::SEND],
            'send-notify-vip-wd-big-sum'        => [Res::SEND],

//            'stp-adjust-callback-regs'          => [Res::STP_ADJUST],
//            'stp-adjust-callback-first-deps'    => [Res::STP_ADJUST],
//            'stp-adjust-callback-repeat-deps'   => [Res::STP_ADJUST],
        ],
        PERIOD_10_MINUTES_OVERLAP => [
            'buf-visits'                        => [Res::BUF],
            'buf-search'                        => [Res::BUF],
            'buf-fingerprint'                   => [Res::BUF],
            'buf-common'                        => [Res::BUF],
            'buf-adjust'                        => [Res::BUF],
            'bonus-templates'                   => P_GI,
        ],
        PERIOD_10_MINUTES_SHIFTED => [
            'users'                             => P_CIS, // Reload some critical tasks
            'update-users-ignores-from-users'   => P_MAIN,
            'update-users-contacts-priority'    => P_MAIN,

            'update-stp-users'                  => [Res::STP, Res::STP_OLD],
            'update-stp-users-customers'        => [Res::STP_OLD],
            'update-adjust-users'               => P_MAIN,
            'rokeente-sessions'                 => [Res::ROK],
            'update-rokeente-sessions-users'    => P_MAIN, // Remove after 2024-10-10
            'update-users-rokeente-sessions-lyra-activity' => P_MAIN,
            'update-checks-users-logins-rokeente-sessions-lyra' => array_fill_keys(P_MAIN, Res::CHECK),
            'update-users-logins-lyra-geo'      => P_MAIN,
            'update-users-lyra-geo-from-transactions'       => P_MAIN,
            'update-users-rokeente-sessions-lyra-geo' => P_MAIN,
            'users-wallets-bonus'               => P_MAIN, //for possible slave lag cover
            'users-wallets-betting'             => P_SMEN_WITH_BETTING, //for possible slave lag cover
            'users-wallets-real'                => P_MAIN, //for possible slave lag cover
        ],
        PERIOD_1_HOUR => [
            'users-remarketing'                 => P_SMEN,
            'users-loyalty'                     => P_CIS,
            'users-loyalty-deposit'             => P_SMEN,
            'users-betting-profit-segments'     => P_GI,
            'users-events'                      => P_SMEN,
            'users-wheel-fortune-prize'         => array_diff(P_SMEN, [Res::GMS, Res::VTR]),
            'users-wheel-fortune-statistics'    => array_diff(P_SMEN, [Res::GMS, Res::VTR]),
            'users-games-chosen'                => P_SMEN,
            'users-payout-limits'               => P_GI,
            'tournaments'                       => P_CIS,
            'sites-mirrors'                     => P_SMEN,
            'sync-bets-white-list-users'        => [Res::CV],
            'update-wmid'                       => [Res::WEBMONEY],
            'update-betting-odds-parts'         => P_BETTING,
            'users-stats'                       => P_YS_MAIN, // Reload because of often slave lags
            'update-users-transactions-dep-number'    => P_ALL,
            'update-tournaments-users-medal-place' => P_GI,

            'bonuses-for-points-product'        => P_SMEN,
            'bonuses-for-points-product-bonus'  => P_SMEN,
            'bonuses-for-points-product-sale'   => P_SMEN,

            'update-users-documents-face-recognize' => [Res::FACES],
            'update-users-documents-text-recognize' => [Res::DEFAULT],
            'update-refcodes-webmaster'         => [Res::DEFAULT],
            'update-users-contacts-login-source' => P_GI,
            'update-checks-sites-1-hour'        => [Res::CHECK],
            'update-checks-all-1-hour'          => [Res::CHECK],
            'update-checks-tasks-queues-1-hour' => [Res::CHECK_CRITICAL],
            'update-metrics-from-bonus-user-progress' => P_SMEN,
            'update-users-metric-chargeback-probability' => P_MAIN,
            'update-users-metric-vip-probability'        => P_MAIN,

            'stp-applications'                  => [Res::STP_OLD],
            'stp-publishers'                    => [Res::STP_OLD],
            'stp-customers-data'                => [Res::STP_OLD],

            'hhs-users-games-sessions'          => [Res::HHS_SMEN, Res::HHS_FLAME, Res::HHS_EE, Res::HHS_WHITE],

            'yhlp-customers-online'             => [Res::YHLP],

            'vipaff-users'                      => [Res::VIPAFF],
            'vipaff-comments'                   => [Res::VIPAFF],
        ],
        PERIOD_1_HOUR_END => [
            'rates-latest'                      => [Res::OXR],
            'ads-stats-facebook'                => [Res::ADS_FACEBOOK],
            'buf-ads-stats-google'              => [Res::BUF],
        ],
        PERIOD_4_HOURS => [
            'update-users-similar-faces-recursive' => [Res::CID],
            'update-users-clouds-sources'       => [Res::CID],
            'update-users-cids'                 => [Res::CID],
            'send-vip-probability-cis'          => [Res::SEND],
            'send-vip-probability-int'          => [Res::SEND],
            'send-vip-probability-upi'          => [Res::SEND],
        ],
        PERIOD_8_HOURS => [
            'crm-service-providers'            => [Res::CRM],
        ],
        PERIOD_PREV_DAY_MIDNIGHT => [
            'update-users-balances-daily'       => P_MAIN,
        ],
        PERIOD_MONTH_MIDNIGHT => [
            'update-users-wallets-snapshots'    => [RES::GGUA],
        ],
        PERIOD_PREV_DAY_HALF_HOUR => [
            'rates-crypto'                      => [Res::COIN_API],
        ],
        PERIOD_PREV_DAY_1_HOUR => [
            'loyalty-statuses'                  => P_CIS,
            'loyalty-deposit-statuses'          => P_SMEN,
            'bonus'                             => P_SMEN,
            'bonus-event-option'                => P_SMEN,
            'lotteries'                         => P_SMEN,
            'lottery-tickets'                   => P_SMEN,
            'lottery-wins'                      => P_SMEN,
            'users-rebills'                     => P_SMEN,
            'hhs-games'                         => [Res::HHS_SMEN, Res::HHS_FLAME, Res::HHS_EE, Res::HHS_WHITE],
            'hhs-projects'                      => [Res::HHS_SMEN, Res::HHS_FLAME, Res::HHS_EE, Res::HHS_WHITE],
            'yhlp-operators'                    => [Res::YHLP],
            'yhlp-events'                       => [Res::YHLP],
            'yhlp-users-time-zone'              => [Res::YHLP],
            'marketing-tids'                    => [Res::MR],
            'marketing-hosts'                   => [Res::MRT],
            'vipaff-refcodes'                   => [Res::VIPAFF, Res::VIPAFF_CP],
            'wp-programs'                       => [Res::WP],
            'wp-webmasters'                     => [Res::WP],
            'wp-hosts'                          => [Res::WP],
            'wp-webmasters-programs'            => [Res::WP],
            'crm-hosts'                         => [Res::CRM],
            's2p-bins'                          => [Res::S2P],
            'employees-mails-public-keys'       => [Res::SYNEF_CLOUD],
            'rokeente-blacklists'               => [Res::ROK],
            'rokeente-tags'                     => [Res::ROK],
            'users-logins-rokeente-sessions'    => [RES::SZL],

            'update-users-ignores-from-logins'   => P_MAIN,
            'update-users-transactions-metrics-full'   => P_MAIN,

            'update-users-games-metrics'        => array_fill_keys(P_MAIN, Q_SLOW),
            'update-users-transactions-game-group'     => P_WITH_BETTING,
            'update-users-games-raw-clean'      => [...P_GI, ...P_YS_MAIN, ...P_YS_SS],
            'update-users-metrics-bets-favorites' => P_WITH_BETTING,
            'update-users-rebills-clean'        => P_SMEN,
            'update-hosts-info-from-hosts'      => P_CIS,
            'update-users-favorite-crm-channel' => array_fill_keys(P_CIS, Q_SLOW),
            'update-split-tests-users-clean'      => P_MAIN,
            'update-users-metric-bonus-excessive' => array_fill_keys(P_MAIN, Q_SLOW),

            'update-refcodes-source-site'       => [Res::DEFAULT],
            'update-bins'                       => [Res::S2P],
            'update-priority-pay-sys'           => [Res::S2P],
            'update-events-log-clean'           => [Res::DEFAULT],
            'update-s2p-users-rank'             => [Res::S2P],
            'update-s2p-projects-flag'          => [Res::S2P],

            'update-crm-letters-opens'          => array_fill_keys(P_MAIN, Q_SLOW),

            'update-checks-sites-1-day'         => [Res::CHECK],
            'update-checks-all-1-day'           => [Res::CHECK],
            'update-checks-tasks-queues-1-day'  => [Res::CHECK_CRITICAL],
            'update-users-games-agg'            => [Res::DEFAULT],
            'update-users-games-payments-activity' => [Res::DEFAULT],

            'update-users-statuses-paid'             => P_MAIN,
            //'update-users-statuses-to-pre-vip'  => P_MAIN,
            'update-users-statuses-daily'       => P_MAIN,
            'update-users-statuses-normal-daily' => array_fill_keys(P_MAIN, Q_SLOW),
            'update-users-statuses-by-rules-daily' => P_MAIN,

            'update-users-metrics-expired-clean' => P_MAIN,

            'sync-withdrawals'                  => $wdQueuesConfig,
        ],
        PERIOD_PREV_DAY_3_HOURS => [
            'send-affiliate-media-buy'          => [Res::SEND],
            'send-income-summary'               => [Res::SEND],
            'send-income-summary-locations'     => [Res::SEND],
            'send-kpi-ru'                       => [Res::SEND],
            'send-kpi-int'                      => [Res::SEND],
            'send-kpi-full-eur'                 => [Res::SEND],
            'send-kpi-ys-int'                   => [Res::SEND],
            'send-payments-comparison-smen'     => [Res::SEND],
            'send-payments-comparison-gi'       => [Res::SEND],
            'send-daily-games'                  => [Res::SEND],
            'send-kpi-ggbet-full'               => [Res::SEND],
            'send-kpi-vbet'                     => [Res::SEND],
            'send-s2p-approve-ratio'            => [Res::SEND],
            'send-s2p-approve-ratio-banks'      => [Res::SEND],
            'send-out-percent-daily'            => [Res::SEND],
            'send-users-bonuses-percent-smen'   => [Res::SEND],
            'send-users-bonuses-percent-gi'     => [Res::SEND],
            'send-users-bonuses-percent-s7-awi-sltr' => [Res::SEND],
            'send-users-deps-daily'             => [Res::SEND],
            'send-users-deps-top-daily-cis'     => [Res::SEND],
            'send-users-deps-top-daily-int'     => [Res::SEND],
            'send-crm-or'                       => [Res::SEND],
            'send-active-users'                 => [Res::SEND],
            'send-vip-activity-progress'        => [Res::SEND],
            'send-payments-approve-ratio-ggbet' => [Res::SEND],
            'send-payments-approve-ratio-vv'    => [Res::SEND],
            'send-payments-approve-ratio-nvc'   => [Res::SEND],
            'send-payments-approve-ratio-icg'   => [Res::SEND],
            'send-payments-fail-ratio'          => [Res::SEND],
            'send-conversion-by-affiliates-gg-casino-betting' => [Res::SEND],
            'send-conversion-by-countries-vv'           => [Res::SEND],
            'send-conversion-by-countries-vbet'         => [Res::SEND],
            'send-conversion-by-countries-ggb'          => [Res::SEND],
            'send-conversion-by-countries-vsp'          => [Res::SEND],
            'send-conversion-by-countries-mrb'          => [Res::SEND],
            'send-conversion-by-countries-icg'          => [Res::SEND],
            'send-conversion-by-countries-s7'           => [Res::SEND],
            'send-conversion-by-countries-win'          => [Res::SEND],
            'send-conversion-by-countries-los'          => [Res::SEND],
            'send-conversion-by-countries-vox'          => [Res::SEND],
            'send-conversion-by-countries-nvc'          => [Res::SEND],
            'send-conversion-by-countries-verde'        => [Res::SEND],
            'send-conversion-by-countries-hit'          => [Res::SEND],
            'send-conversion-by-countries-vv-wm-66340'  => [Res::SEND],
            'send-conversion-by-countries-vv-wm-65156'  => [Res::SEND],
            'send-conversion-by-countries-vv-wm-103407' => [Res::SEND],
            'send-conversion-by-countries-sltr'         => [Res::SEND],
            'send-conversion-by-countries-awi'          => [Res::SEND],
            'send-incorrect-affiliates-refcodes'        => [Res::SEND],
            'send-crm-daily-rev-share-int'              => [Res::SEND],
            'send-crm-daily-rev-share-cis'              => [Res::SEND],
            'send-stp-payments'                         => [Res::SEND],
            'send-stp-payments-month'                   => [Res::SEND],
            'send-vbet-daily-profit'                    => [Res::SEND],
            'send-vsp-daily-profit'                     => [Res::SEND],
            'send-revenue-distribution-ggb'             => [Res::SEND],
            'send-revenue-distribution-vb'              => [Res::SEND],
            'send-betting-summary-ggb'                  => [Res::SEND],
            'send-betting-summary-vb'                   => [Res::SEND],
            'send-betting-summary-vsp'                  => [Res::SEND],
            'send-crm-top-payments-countries-vv'        => [Res::SEND],
            'send-crm-top-payments-countries-icg'       => [Res::SEND],
            'send-crm-top-payments-countries-ggb'       => [Res::SEND],
            'send-crm-top-payments-countries-verde'     => [Res::SEND],
            'send-crm-top-payments-countries-vbet'      => [Res::SEND],
            'send-crm-top-payments-countries-s7'        => [Res::SEND],
            'send-crm-top-payments-countries-hit'       => [Res::SEND],
            'send-conversion-fail-summary-ggb'          => [Res::SEND],
            'send-crm-top-payments-countries-ggbetua'   => [Res::SEND],
            'send-crm-top-payments-countries-nvc'       => [Res::SEND],
            'send-crm-top-payments-countries-win'       => [Res::SEND],

            'send-notify-new-big-dep-users'     => [Res::SEND],
            'send-notify-week-big-dep-users'    => [Res::SEND],
            'send-notify-big-dep-users-vv'      => [Res::SEND],
            'send-notify-vip-big-dep'           => [Res::SEND],
            'send-notify-vip-new-dep-info'      => [Res::SEND],
            'send-notify-vip-awol-dep-info'     => [Res::SEND],
            'send-notify-vip-risk-dep-info'     => [Res::SEND],
            'send-notify-vip-dep-info'          => [Res::SEND],
            'send-notify-jelly-dep-info'        => [Res::SEND],
            'send-notify-ultra-no-dep'          => [Res::SEND],
            'send-notify-vip-no-dep'            => [Res::SEND],
            'send-notify-asp-no-dep'            => [Res::SEND],
            'send-notify-ultra-first-dep'       => [Res::SEND],
            'send-notify-ultra-no-contact'      => [Res::SEND],
            'send-notify-users-inactive'        => [Res::SEND],
            'send-notify-cg-big-deps'           => [Res::SEND],
            'send-notify-vip-inactive-deposit'  => [Res::SEND],
            'send-users-reg-proton'             => [Res::SEND],
            'send-fd-dynamic'                   => [Res::SEND],
            'send-deps-metrics-daily'           => [Res::SEND],
            'send-s2p-notify-documents-wait-cis' => [Res::SEND],
            'send-s2p-notify-documents-wait-int' => [Res::SEND],
            'send-new-canonical-pay-sys'        => [Res::SEND],
            'send-checkbox-receipts'            => [Res::SEND],

            // Cherry
            'send-notify-cid-vip-awol-dep-to-parent-pm-cherry' => [Res::SEND],
            'send-notify-weekend-big-deps'      => [Res::SEND],

            'update-lyra-users-scenarios'       => array_fill_keys(P_MAIN, Q_SLOW),
            'update-lyra-users-scenarios-clean' => P_MAIN,
            'update-lyra-scenarios-stat'        => [Res::DEFAULT => Q_SLOW],
            'delete-not-used-lyra-scenarios'    => [Res::DEFAULT],
            //'update-lyra-scenarios-predictions' => [Res::DEFAULT], Temporary off, not used and very slow, need refactoring
            'update-lyra-geo-sum-stat'          => [Res::DEFAULT],
            'update-lyra-feature-set-stat'      => [Res::DEFAULT],
            'update-lyra-device-financial-activity-stat' => [Res::DEFAULT],
            'update-lyra-stream-entity-distribution' => [Res::DEFAULT],
        ],
        PERIOD_PREV_DAY_4_HOURS => [
            'update-stp-users'                  => [Res::STP],
            'update-stp-users-customers'        => [Res::STP],
            'hhs-users-games-sessions'          => [Res::HHS_SMEN, Res::HHS_FLAME, Res::HHS_EE, Res::HHS_WHITE], // Reload some critical tasks for whole previous day
            'update-users-last-activity'        => [...P_MAIN, ...P_YS_SS],
            'update-adjust-users'               => P_MAIN,
            'users-segmentation-ys-ml-ggbet'    => [Res::YS_ML_SEG],
            'update-countries-deps-distr'       => [Res::DEFAULT],
            'update-users-documents-delete-from-bin' => [Res::DEFAULT],
        ],
        PERIOD_PREV_DAY_7_HOURS => [
            'ads-stats-unity'                   => [Res::ADS_UNITY],
            'ads-stats-iron'                    => [Res::ADS_IRON],
            'ads-stats-vungle'                  => [Res::ADS_VUNGLE],
            'ads-stats-google'                  => [Res::ADS_GOOGLE],
            'ads-stats-mintegral'               => [Res::ADS_MINTEGRAL],
            'wp-cpa'                            => [Res::WP],
            'update-wp-cpa'                     => [Res::WP],
            'send-users-games-comparison'       => [Res::SEND],
        ],
        PERIOD_PREV_2_WEEKS_7_HOURS => [
            'ads-stats-facebook'            => [Res::ADS_FACEBOOK],
        ],
        PERIOD_CUR_DAY_7_HOURS => [
            'send-notify-expiring-pgp'          => [Res::SEND],
        ],
        PERIOD_CUR_DAY_8_HOURS_EEST => [
            'send-rokeente-tags'                => [Res::SEND],
        ],
        PERIOD_CUR_DAY_10_HOURS_EEST => [
            'send-notify-vip-wd-process'        => [Res::SEND],
        ],
        PERIOD_CUR_DAY_18_HOUR => [
            'send-income-summary-evening'       => [Res::SEND],
        ],
        PERIOD_MONDAY => [
            'send-confirm-rate'                     => [Res::SEND],
            'send-crm-cf'                           => [Res::SEND],
            'send-crm-or-vs'                        => [Res::SEND],
            'send-out-percent-weekly'               => [Res::SEND],
            'send-s2p-approve-ratio-ggbet-week'     => [Res::SEND],
            'send-vip-pm-cid-contacts-week'         => [Res::SEND],
            'send-users-deps-weekly'                => [Res::SEND],
            'send-crm-weekly-rev-share-int'         => [Res::SEND],
            'send-crm-weekly-rev-share-cis'         => [Res::SEND],
            'send-crm-web-push'                     => [Res::SEND],
            'send-crm-viber'                        => [Res::SEND],
//            'send-trustly-users-info-fr-su'       => [SEND], // TODO: remove
            'send-top-pay-country-ptg-ggbet-week'   => [Res::SEND],
            'send-mobile-apps-funnel'               => [Res::SEND],
            'send-mobile-apps-funnel-gi'            => [Res::SEND],
            'send-mobile-apps-funnel-cg'            => [Res::SEND],
            'send-deps-metrics-weekly'              => [Res::SEND],
            'send-users-games-casino-weekly-ggb'    => [Res::SEND],
            'send-users-games-casino-weekly-ggua'   => [Res::SEND],

            'sync-bonus-black-list-add-users'       => P_SMEN,
            'sync-bonus-black-list-add-cid-users'   => P_SMEN,
            'sync-bonus-black-list-remove-users'    => P_SMEN,

            'update-sites-emails-domains'           => P_MAIN,
            'update-users-statuses-by-rules-weekly' => P_MAIN,
        ],
        PERIOD_FRIDAY => [
//            'send-trustly-users-info-mo-th'     => [SEND], // TODO: remove
            'send-ultra-pm-cid-contacts-week'   => [Res::SEND],
        ],
        PERIOD_MON_WED_FRI => [
            'send-cohort-daily-dep-ggb'         => [Res::SEND],
            'send-cohort-daily-bet-ggb'         => [Res::SEND],
            'send-cohort-daily-retention-ggb'   => [Res::SEND],
        ],
        PERIOD_MORN_TUE_TILL_SAT => [
            'send-vip-users-night-calls-tue-till-sut' => [Res::SEND],
        ],
        PERIOD_MORN_SUN_MON => [
            'send-vip-users-night-calls-sun-mon' => [Res::SEND],
        ],
        PERIOD_TUE_TILL_SAT_9_HOURS_EEST => [
            'send-ultra-users-night-calls-tue-till-sut' => [Res::SEND],
        ],
        PERIOD_SUN_MON_9_HOURS_EEST => [
            'send-ultra-users-night-calls-sun-mon' => [Res::SEND],
        ],
        PERIOD_1_8_15_22_DOM => [
            'send-vip-activity-summary'         => [Res::SEND],
        ],
        PERIOD_MONTH_BEGIN => [
            'update-users-statuses-by-rules-monthly'     => P_MAIN,
            'send-out-percent-monthly'     => [Res::SEND],
            'send-ultra-pm-cid-contacts-month'  => [Res::SEND],
            'send-vip-pm-cid-contacts-month'    => [Res::SEND],
            'send-users-games-statistics-int'   => [Res::SEND],
            'send-users-games-statistics-cis'   => [Res::SEND],
            'send-out-percent-by-country'       => [Res::SEND],
            'update-employees-permissions'      => [Res::DEFAULT],
            'update-users-statuses-normal-monthly' => array_fill_keys(P_MAIN, Q_SLOW),
            'send-top-pay-country-ptg-ggbet-month' => [Res::SEND],
            'update-users-clean'                => P_YS_MAIN,
        ],
        PERIOD_HALF_MONTH => [
            'send-speed-out-transactions'      => [Res::SEND],
        ],
    ],
    'dependencies' => [
        // 1 minute
        ['update-users-contacts-priority', P_SMEN, 'users'],
        ['update-users-contacts-priority', P_YS_MAIN, 'users'],
        ['update-checks-users-stats-lyra', P_GI, 'users-stats'],
        ['update-users-ignores-from-users', P_MAIN, 'users'],
        ['update-users-contacts-from-users', P_MAIN, 'users'],

        // 3 minutes
        ['ignore-users', P_SMEN, 'users'],
        ['update-users-transactions-firsts', P_MAIN, 'users-stats'],
        ['update-users-transactions-metrics-incremental', P_MAIN, 'users-stats'],
        ['update-withdrawals-delay', P_MAIN, 'users-stats'],
        ['update-users-logins-reg', P_MAIN, 'users-logins'],
        ['update-users-from-logins', P_MAIN, 'users', 'update-users-logins-reg'],
        ['update-users-special-info-from-logins', P_MAIN, 'users', 'update-users-logins-reg'],
        ['update-users-requisites-from-s2p-orders', [Res::S2P], 's2p-orders' => P_S2P],
        ['update-users-requisites-from-users-transactions', P_GI, 'users-stats'],
        ['update-users-contacts-from-users-requisites', P_GI, 'update-users-requisites-from-users-transactions'],
        ['update-users-contacts-priority', P_GI, 'update-users-contacts-from-users-requisites', 'users'],
        ['update-users-games-aggregate', [...P_GI, ...P_YS_MAIN, ...P_YS_SS], 'users-games-raw'],
        ['update-users-games-aggregate', P_GI, 'users-logins'],
        ['update-betting-log-from-logins', P_GI_WITH_BETTING, 'betting-log', 'users-logins'],
        ['update-users-games-from-betting-log', P_GI_WITH_BETTING, 'betting-log'],
        ['update-users-last-activity', P_SMEN, 'users-games-tokens'],
        ['update-users-last-activity', [...P_SMEN_WITH_BETTING, ...P_YS_MAIN], 'users-games-bets'],
        ['update-users-last-activity', [...P_GI, ...P_YS_MAIN, ...P_YS_SS], 'update-users-games-aggregate'],
        ['update-users-last-activity', P_GI_WITH_BETTING, 'update-users-games-from-betting-log'],
        ['update-users-metrics-bets-first-at', P_SMEN, 'users-games-tokens'],
        ['update-users-metrics-bets-first-at', [...P_SMEN_WITH_BETTING, ...P_YS_MAIN], 'users-games-bets'],
        ['update-users-metrics-bets-first-at', P_GI, 'update-users-games-aggregate'],
        ['update-users-metrics-bets-first-at', P_GI_WITH_BETTING, 'update-users-games-from-betting-log'],
        ['update-s2p-orders-firsts', [Res::S2P], 's2p-orders' => P_S2P],
        ['update-betting-odds-parts', P_BETTING, 'betting-bets'],

        // 5 minutes
        ['jira-files', [Res::JIRA], 'jira-issues'],

        // 10 minutes
        ['users-unsubscribe', P_CIS, 'users'],
        ['users-stats-contexts', P_YS_MAIN, 'users-stats'],
        ['update-users-transactions-login', P_MAIN, 'users-logins', 'users-stats'],
        ['update-users-stats-details-from-login', P_GI, 'users-stats', 'users-logins'],

        ['update-stp-users', [Res::STP, Res::STP_OLD], 'stp-events', 'users' => P_MAIN, 'update-users-from-logins' => P_MAIN],
        ['update-stp-users-customers', [Res::STP, Res::STP_OLD], 'update-stp-users'],

        ['update-adjust-users', P_MAIN, 'users'],

        ['yhlp-chats-messages', Res::YHLP, 'yhlp-chats'],
        ['yhlp-chats-topics', [Res::YHLP], 'yhlp-topics'],

        ['update-affiliates-codes-stats-from-visits-log', P_YS_MAIN, 'visits-log-ys'],

        ['update-split-tests-logs-users', P_SMEN, 'split-tests-logs', 'split-tests-users'],
        ['update-withdrawals-auto-process', P_SMEN, 'users-wallets-real', 'update-users-transactions-metrics-incremental'],
        ['update-checks-users-logins-rokeente-sessions-lyra', P_MAIN, 'users-logins', 'rokeente-sessions' => Res::ROK],
        ['update-rokeente-sessions-users', P_MAIN, 'users-logins'],
        ['rokeente-sessions', [Res::ROK]],
        ['update-users-rokeente-sessions-lyra-activity', P_MAIN, 'update-rokeente-sessions-users'],
        ['update-users-logins-lyra-geo', P_MAIN, 'users-logins'],
        ['update-users-lyra-geo-from-transactions', P_MAIN, 'users-stats'],
        ['update-users-rokeente-sessions-lyra-geo', P_MAIN, 'update-rokeente-sessions-users'],

//        ['stp-adjust-callback-regs' , [Res::STP_ADJUST], 'users' => [Res::VCUA], 'update-users-from-logins' => [Res::VCUA]],
//        ['stp-adjust-callback-first-deps', [Res::STP_ADJUST], 'update-users-transactions-firsts' => [Res::VCUA]],
//        ['stp-adjust-callback-repeat-deps', [Res::STP_ADJUST], 'update-users-transactions-firsts' => [Res::VCUA]],

        ['update-users-transactions-dep-number', P_MAIN, 'users-stats'],

        ['update-users-statuses-paid', P_MAIN, 'users-stats'],
        ['update-users-statuses-to-pre-vip', P_MAIN, 'update-users-transactions-firsts'],

        // 1 hour
        ['update-tournaments-users-medal-place', P_GI, 'tournaments-users'],

        // 1 day
        ['update-users-transactions-metrics-full', P_MAIN, 'users-stats'],
        ['update-users-transactions-metrics-full', P_GI, 'users-stats-bonuses'],
        ['update-users-transactions-game-group', P_GI_WITH_BETTING, 'update-users-games-from-betting-log', 'update-users-games-aggregate'],
        ['update-users-transactions-game-group', [Res::MRB], 'update-users-games-aggregate'],
        ['update-users-transactions-game-group', P_SMEN_WITH_BETTING, 'users-games-tokens'],
        ['update-users-transactions-game-group', P_WITH_BETTING, 'users-stats'],

        ['update-lyra-scenarios-stat', [Res::DEFAULT], 'update-lyra-users-scenarios' => P_MAIN],
        ['delete-not-used-lyra-scenarios', [Res::DEFAULT], 'update-lyra-scenarios-stat'],
        ['update-lyra-scenarios-predictions', [Res::DEFAULT], 'delete-not-used-lyra-scenarios'],
        ['update-wp-cpa', [Res::WP], 'wp-cpa'],

        ['update-users-statuses-daily', P_MAIN, 'update-users-transactions-metrics-full'],
        ['update-users-statuses-normal-daily', P_MAIN, 'update-users-statuses-daily'],

        // 1 month
        ['update-users-statuses-by-rules-monthly', P_MAIN, 'users-stats'],

    ],
];
