<template>
    <card width="wide">
        <richTable
            v-bind="richTable"
            showReset
            @reload="onReload"
        >
            <template #afterTitle="{refreshCallback}">
                <popover
                    title="Add threshold"
                    @open="onCreateOpen"
                >
                    <button
                        type="button"
                        class="btn btn-sm btn-success"
                    >
                        <icona name="icn-plus" /> Add
                    </button>
                    <template #content>
                        <form-grid
                            v-bind="createForm"
                            @change="createForm.values = $event"
                            @submit="onCreateSubmit($event, refreshCallback)"
                        />
                    </template>
                </popover>
            </template>
            <template #toStatus="{row}">
                <span :title="row.fromStatuses">{{ row.toStatus }}</span>
            </template>
            <template #isActive="{row, refreshCallback}">
                <button
                    type="button"
                    class="btn btn-xs"
                    :class="row.isActive ? 'btn-success' : 'btn-secondary'"
                    @click="onUpdate({id: row.id, column: 'isActive', isActive: !row.isActive }, refreshCallback)"
                >
                    <icona :name="row.isActive ? 'icn-check' : 'icn-pause'" />
                </button>
            </template>
            <template #isUp="{row}">
                <icona :name="row.isUp ? 'icn-arrow-big-up-lines' : 'icn-arrow-big-down-lines'" :class="row.isUp ? 'text-success' : 'text-danger'" />
            </template>
            <template #amount="{row, refreshCallback}">
                <inplaceEdit
                    :value="row.amount"
                    type="input"
                    @submit="onUpdate({id: row.id, column: 'amount', amount: $event }, refreshCallback)"
                />
            </template>
            <template #period="{row, refreshCallback}">
                <inplaceEdit
                    :value="row.period"
                    :list="availablePeriods"
                    type="select"
                    @submit="onUpdate({id: row.id, column: 'period', period: $event }, refreshCallback)"
                />
            </template>
            <template #checkPeriod="{row, refreshCallback}">
                <inplaceEdit
                    :value="row.checkPeriod"
                    :list="availableCheckPeriods"
                    type="select"
                    @submit="onUpdate({id: row.id, column: 'checkPeriod', checkPeriod: $event }, refreshCallback)"
                />
            </template>
            <template #delete="{row, refreshCallback}">
                <button
                    class="btn btn-danger btn-xs"
                    :disabled="row.deleteDisabled"
                    @click="onDelete({id: row.id}, refreshCallback)"
                >
                    <icona name="icn-delete" /> Delete
                </button>
            </template>
        </richTable>
    </card>
</template>

<script lang="ts">
import { RichTable, InplaceEdit, FormGrid, Popover, Icona } from '@/components'
import { Card } from '@/widgets'
import { defineComponent } from 'vue'
import { FormGridType, Item, NextWithReload, RichTableType, Values } from '@/types'

interface VipThresholdRow {
    id: number
    country: string
    countryCode: string
    toStatus: string
    fromStatuses: string
    status: number
    activeStatus: number
    amount: string
    updatedAt: string
    updatedBy: string
    siteId: number
    site: string
    isUp: boolean
    isActive: boolean
    period: string
    checkPeriod: number
}

export default defineComponent({
    components: {
        Card,
        RichTable,
        InplaceEdit,
        FormGrid,
        Popover,
        Icona,
    },

    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },

    data () {
        return {
            richTable: {} as RichTableType<VipThresholdRow>,
            createForm: {} as FormGridType,
            availablePeriods: [] as Item[],
            availableCheckPeriods: [] as Item[],
        }
    },

    methods: {
        onReload (params: Values) {
            this.$historyReplaceParams(params)
            return this.reload(params)
        },
        reload (params: Values) {
            this.$processRichTableResponse(this.$fetch(this.$route.path + '/data', params), this.richTable).then((data) => {
                this.availablePeriods = data.availablePeriods
                this.availableCheckPeriods = data.availableCheckPeriods
            })
        },
        async onCreateOpen () {
            this.createForm = await this.$fetch(this.$route.path + '/add-form')
        },
        async onCreateSubmit (params: Values, refreshCallBack: () => void) {
            return this.$processFormResponse(this.$fetch(this.$route.path + '/add', params), this.createForm).then(() => refreshCallBack())
        },
        onUpdate (params: Values, refreshCallback: () => void) {
            this.$fetch(this.$route.path + '/update', params).then(refreshCallback)
        },
        onDelete (params: Values, refreshCallback: () => void) {
            if (confirm('Do you want to delete thresholds for ' + params.country + '?')) {
                this.$fetch(this.$route.path + '/delete', params).then(refreshCallback)
            }
        },
    },
})
</script>
