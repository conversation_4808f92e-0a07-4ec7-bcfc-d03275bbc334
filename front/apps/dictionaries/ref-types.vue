<template>
    <card>
        <rich-table
            v-bind="richTable"
            showPagination
            showRefresh
            @reload="onReload"
        >
            <template #afterTitle="{refreshCallback}">
                <popover
                    title="Create new"
                    position="right"
                    @open="onCreateOpen"
                >
                    <button
                        type="button"
                        class="btn btn-sm btn-success"
                    >
                        <icona name="icn-plus" /> Add
                    </button>
                    <template #content>
                        <form-grid
                            v-bind="createForm"
                            @change="createForm.values = $event"
                            @submit="onCreateSubmit($event, refreshCallback)"
                        />
                    </template>
                </popover>
            </template>

            <template #actions="{row, refreshCallback}">
                <div class="btn-group btn-group-xs">
                    <button
                        type="button"
                        class="btn btn-warning"
                        title="Refresh"
                        @click="onRefresh(row, refreshCallback)"
                    >
                        <icona name="icn-refresh" /> Refresh
                    </button>
                    <button
                        type="button"
                        class="btn btn-xs btn-primary"
                        @click="onCount(row.value, row.value_operator, refreshCallback)"
                    >
                        <icona name="icn-info" /> Count
                    </button>
                    <button
                        type="button"
                        class="btn btn-danger"
                        title="Delete"
                        @click="onDelete(row, refreshCallback)"
                    >
                        <icona name="icn-delete" />
                    </button>
                </div>
            </template>
        </rich-table>
    </card>
</template>

<script lang="ts">
import { RichTable, Popover, FormGrid, Icona } from '@/components'
import { Card } from '@/widgets'
import { defineComponent } from 'vue'
import { FormGridType, NextWithReload, TableRow, Values } from '@/types'

export default defineComponent({
    components: {
        Icona,
        Card,
        RichTable,
        Popover,
        FormGrid,
    },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },
    data: function () {
        return {
            richTable: {},
            createForm: {} as FormGridType,
        }
    },

    methods: {
        onReload (params: Values) {
            this.$historyReplaceParams(params)
            return this.reload(params)
        },
        reload (params: Values) {
            this.$processRichTableResponse(this.$fetch(this.$route.path + '/data', params), this.richTable)
        },
        async onCreateOpen () {
            this.createForm = await this.$fetch(this.$route.path + '/create-form')
        },
        onCreateSubmit (data: Values, refreshCallback: ()=> void) {
            return this.$processFormResponse(this.$fetch(this.$route.path + '/create', data), this.createForm).then(refreshCallback)
        },
        onDelete (row: TableRow, refreshCallback: ()=> void) {
            if (!confirm('Sure?')) {
                return
            }

            return this.$fetch(this.$route.path + '/delete', { id: row.id }).then(refreshCallback)
        },
        onRefresh (row: TableRow, refreshCallback: ()=> void) {
            const date = prompt('Date of reload (today, yesterday, -10 days, -1 month, 2019-09-24, ...)', 'yesterday')

            if (!date) {
                return
            }

            return this.$fetch(this.$route.path + '/refresh', { id: row.id, value: date }).then(refreshCallback)
        },
        onCount(value: string, operator: number, refreshCallback: () => void) {
            this.$fetch(this.$route.path + '/count', { value: value, operator: operator })
                .then(refreshCallback)
        },
    },
})
</script>
