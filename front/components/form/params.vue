<template>
    <template v-for="(inputSet, setI) in inputsSets" :key="`inputSet${setI}`">
        {{ inputSet.title }}
        <div
            :ref="`ddSet${setI}`"
            class="form-params rounded-3 bg-tet"
            :class="{'bg-body-tertiary': setI === sortableActiveSet}"
            :data-block="setI"
        >
            <template v-for="(filter, _filterI) in inputSet.inputs" :key="`input${_filterI}`">
                <div
                    class="m-1 d-inline-flex w-auto draggable"
                    :data-name="filter.name"
                >
                    <button
                        type="button"
                        class="btn btn-outline-secondary hidden-on-form-params drag-helper"
                    >
                        {{ filter.title }}
                    </button>
                    <div class="input-group input-group-sm hidden-on-form-list">
                        <button
                            type="button"
                            class="btn"
                            :class="filterClass(filter)"
                            :title="help[filter.name]"
                            :disabled="disabled"
                            @click="onParamClick(filter)"
                        >
                            {{ filter.title }}
                        </button>
                        <span
                            v-if="filter.name in errors"
                            class="input-group-text text-danger"
                        >{{ errors[filter.name] }}</span>
                    </div>
                </div>
                <help-hint
                    :showEdit="hintsEditOpened"
                    :description="help[filter.name]"
                    @submit="onHintSubmit(filter.name, $event)"
                />
            </template>
        </div>
        <hr v-if="setI !== (inputsSets.length - 1)" class="my-2">
    </template>
</template>

<script lang="ts">
import HelpHint from './help-hint.vue'
import { defineComponent, PropType } from 'vue'
import Sortable, { SortableEvent } from 'sortablejs'
import { FormInput, FormInputsSet, FormValue, Value } from '@/types.ts'

const sortInstances = [] as Sortable[]

export default defineComponent({
    components: {
        HelpHint,
    },
    props: {
        inputsSets: {
            type: Array as PropType<FormInputsSet[]>,
            required: true,
        },
        help: {
            type: Object as PropType<Record<string, string>>,
            default: () => ({}),
        },
        hintsEditOpened: {
            type: Boolean,
            default: false,
        },
        formValues: {
            type: Array as PropType<FormValue[]>,
            required: true,
        },
        errors: {
            type: Object,
            default: () => ({}),
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        dragAndDrop: {
            type: Boolean,
            default: false,
        },
        sortableActiveSet: {
            type: Number,
            default: undefined,
        },
    },
    emits: ['param-click', 'hint-submit', 'sortable-start', 'sortable-end'],
    watch: {
        inputsSets: {
            handler () {
                if (this.dragAndDrop) {
                    this.sortableDestroy()
                    this.sortableInit()
                }
            },
            flush: 'post',
            deep: true,
        },
    },
    unmounted () {
        if (this.dragAndDrop) {
            this.sortableDestroy()
        }
    },
    methods: {
        onParamClick (input: FormInput) {
            this.$emit('param-click', input)
        },
        onHintSubmit (name: string, value: Value) {
            this.$emit('hint-submit', name, value)
        },
        filterClass (input: FormInput) {
            if (input.name in this.errors) {
                return 'btn-outline-danger'
            }

            if (this.formValues.map(f => f.name).includes(input.name)) {
                return 'btn-primary'
            }

            return 'btn-secondary'
        },
        onSortableEnd () {
            this.$emit('sortable-end')
        },
        onSortableStart (evt: SortableEvent) {
            this.$emit('sortable-start', parseInt(evt.to.dataset.block as string))
        },
        onSortableRemove(evt: SortableEvent) {
            evt.from.insertBefore(evt.item, evt.clone)
            evt.clone.remove()
        },
        sortableInit() {
            this.inputsSets.forEach((_, i) => {
                sortInstances.push(Sortable.create((this.$refs[`ddSet${i}`] as HTMLElement[])[0], {
                    group: {
                        name: 'report-filters-set-' + i,
                        pull: 'clone',
                    },
                    sort: false,
                    onStart: this.onSortableStart,
                    onEnd: this.onSortableEnd,
                    onRemove: this.onSortableRemove,
                }))
            })
        },
        sortableDestroy() {
            sortInstances.forEach(inst => inst.destroy())
            sortInstances.splice(0)
        },
    },
})
</script>
<style lang="scss">
.form-params .hidden-on-form-params {
    display: none
}
</style>
